use anyhow::Result;
use thiserror::Error;
use uuid::Uuid;
use chrono::{Utc, Duration};
use sqlx::PgPool;
use tracing::{info, warn, error};
use rand::{thread_rng, Rng};
use base64::{Engine as _, engine::general_purpose};

use crate::models::email_verification::{
    EmailVerificationToken, SendVerificationEmailRequest, SendVerificationEmailResponse,
    VerifyEmailRequest, VerifyEmailResponse, RateLimitInfo
};
use crate::models::User;
use crate::utils::errors::AppError;
use crate::services::email_service::{EmailService, EmailError};

#[derive(Error, Debug)]
pub enum EmailVerificationError {
    #[error("Database error: {0}")]
    DatabaseError(String),
    #[error("User not found")]
    UserNotFound,
    #[error("Email already verified")]
    EmailAlreadyVerified,
    #[error("Invalid or expired token")]
    InvalidToken,
    #[error("Token already used")]
    TokenAlreadyUsed,
    #[error("Rate limit exceeded")]
    RateLimitExceeded,
    #[error("Email service error: {0}")]
    EmailServiceError(String),
}

#[derive(Clone)]
pub struct EmailVerificationService {
    pool: PgPool,
    email_service: EmailService,
}

impl EmailVerificationService {
    pub fn new(pool: PgPool, email_service: EmailService) -> Self {
        Self { pool, email_service }
    }

    /// Generate and send email verification token
    pub async fn send_verification_email(
        &self,
        request: SendVerificationEmailRequest,
    ) -> Result<SendVerificationEmailResponse, EmailVerificationError> {
        info!("Sending verification email to: {}", request.email);

        // Check if user exists and get user info
        let user = self.get_user_by_email(&request.email).await?;

        // Check if email is already verified
        if user.is_verified {
            return Ok(SendVerificationEmailResponse {
                success: true,
                message: "Email is already verified".to_string(),
                email_sent: false,
                rate_limit_info: None,
            });
        }

        // Check rate limiting (max 3 emails per hour)
        if !request.resend.unwrap_or(false) {
            if let Some(rate_limit_info) = self.check_rate_limit(user.id).await? {
                return Ok(SendVerificationEmailResponse {
                    success: false,
                    message: "Rate limit exceeded. Please wait before requesting another verification email.".to_string(),
                    email_sent: false,
                    rate_limit_info: Some(rate_limit_info),
                });
            }
        }

        // Invalidate any existing tokens for this user
        self.invalidate_existing_tokens(user.id).await?;

        // Generate new verification token
        let token = self.generate_verification_token();
        let expires_at = Utc::now() + Duration::hours(24); // 24-hour expiration

        // Store token in database
        let _verification_token = self.store_verification_token(user.id, &token, expires_at).await?;

        // Send email via email service
        match self.email_service.send_verification_email(&request.email, &user.username, &token).await {
            Ok(()) => {
                info!("Verification email sent successfully to: {}", request.email);
            }
            Err(e) => {
                error!("Failed to send verification email to {}: {}", request.email, e);
                return Err(EmailVerificationError::EmailServiceError(e.to_string()));
            }
        }

        Ok(SendVerificationEmailResponse {
            success: true,
            message: "Verification email sent successfully".to_string(),
            email_sent: true,
            rate_limit_info: None,
        })
    }

    /// Verify email using token
    pub async fn verify_email(&self, request: VerifyEmailRequest) -> Result<VerifyEmailResponse, EmailVerificationError> {
        info!("Verifying email with token: {}", &request.token[..8]);

        // Get token from database
        let token_record = self.get_verification_token(&request.token).await?;

        // Check if token is expired
        if token_record.expires_at < Utc::now() {
            warn!("Expired verification token used: {}", &request.token[..8]);
            return Err(EmailVerificationError::InvalidToken);
        }

        // Check if token is already used
        if token_record.used_at.is_some() {
            warn!("Already used verification token: {}", &request.token[..8]);
            return Err(EmailVerificationError::TokenAlreadyUsed);
        }

        // Mark token as used
        self.mark_token_as_used(token_record.id).await?;

        // Update user as verified
        self.mark_user_as_verified(token_record.user_id).await?;

        info!("Email successfully verified for user: {}", token_record.user_id);

        Ok(VerifyEmailResponse {
            success: true,
            message: "Email verified successfully".to_string(),
            email_verified: true,
            user_id: Some(token_record.user_id),
        })
    }

    /// Resend verification email
    pub async fn resend_verification_email(&self, email: &str) -> Result<SendVerificationEmailResponse, EmailVerificationError> {
        let request = SendVerificationEmailRequest {
            email: email.to_string(),
            resend: Some(true),
        };
        
        self.send_verification_email(request).await
    }

    // Private helper methods

    async fn get_user_by_email(&self, email: &str) -> Result<User, EmailVerificationError> {
        let query = "SELECT * FROM users WHERE email = $1";
        
        sqlx::query_as::<_, User>(query)
            .bind(email)
            .fetch_one(&self.pool)
            .await
            .map_err(|e| match e {
                sqlx::Error::RowNotFound => EmailVerificationError::UserNotFound,
                _ => EmailVerificationError::DatabaseError(e.to_string()),
            })
    }

    async fn check_rate_limit(&self, user_id: Uuid) -> Result<Option<RateLimitInfo>, EmailVerificationError> {
        let one_hour_ago = Utc::now() - Duration::hours(1);
        
        let query = r#"
            SELECT COUNT(*) as count
            FROM email_verification_tokens
            WHERE user_id = $1 AND created_at > $2
        "#;

        let (count,): (i64,) = sqlx::query_as(query)
            .bind(user_id)
            .bind(one_hour_ago)
            .fetch_one(&self.pool)
            .await
            .map_err(|e| EmailVerificationError::DatabaseError(e.to_string()))?;

        if count >= 3 {
            let reset_time = Utc::now() + Duration::hours(1);
            return Ok(Some(RateLimitInfo {
                attempts_remaining: 0,
                reset_time,
                window_duration_seconds: 3600,
            }));
        }

        Ok(None)
    }

    async fn invalidate_existing_tokens(&self, user_id: Uuid) -> Result<(), EmailVerificationError> {
        let query = r#"
            UPDATE email_verification_tokens
            SET used_at = NOW()
            WHERE user_id = $1 AND used_at IS NULL
        "#;

        sqlx::query(query)
            .bind(user_id)
            .execute(&self.pool)
            .await
            .map_err(|e| EmailVerificationError::DatabaseError(e.to_string()))?;

        Ok(())
    }

    fn generate_verification_token(&self) -> String {
        let mut rng = thread_rng();
        let bytes: [u8; 32] = rng.random();
        general_purpose::URL_SAFE_NO_PAD.encode(bytes)
    }

    async fn store_verification_token(
        &self,
        user_id: Uuid,
        token: &str,
        expires_at: chrono::DateTime<Utc>,
    ) -> Result<EmailVerificationToken, EmailVerificationError> {
        let query = r#"
            INSERT INTO email_verification_tokens (user_id, token, expires_at)
            VALUES ($1, $2, $3)
            RETURNING *
        "#;

        sqlx::query_as::<_, EmailVerificationToken>(query)
            .bind(user_id)
            .bind(token)
            .bind(expires_at)
            .fetch_one(&self.pool)
            .await
            .map_err(|e| EmailVerificationError::DatabaseError(e.to_string()))?;

        // Return the created token
        let created_token = sqlx::query_as::<_, EmailVerificationToken>(query)
            .bind(user_id)
            .bind(token)
            .bind(expires_at)
            .fetch_one(&self.pool)
            .await
            .map_err(|e| EmailVerificationError::DatabaseError(e.to_string()))?;

        Ok(created_token)
    }

    async fn get_verification_token(&self, token: &str) -> Result<EmailVerificationToken, EmailVerificationError> {
        let query = "SELECT * FROM email_verification_tokens WHERE token = $1";
        
        sqlx::query_as::<_, EmailVerificationToken>(query)
            .bind(token)
            .fetch_one(&self.pool)
            .await
            .map_err(|e| match e {
                sqlx::Error::RowNotFound => EmailVerificationError::InvalidToken,
                _ => EmailVerificationError::DatabaseError(e.to_string()),
            })
    }

    async fn mark_token_as_used(&self, token_id: Uuid) -> Result<(), EmailVerificationError> {
        let query = r#"
            UPDATE email_verification_tokens
            SET used_at = NOW()
            WHERE id = $1
        "#;

        sqlx::query(query)
            .bind(token_id)
            .execute(&self.pool)
            .await
            .map_err(|e| EmailVerificationError::DatabaseError(e.to_string()))?;

        Ok(())
    }

    async fn mark_user_as_verified(&self, user_id: Uuid) -> Result<(), EmailVerificationError> {
        let query = r#"
            UPDATE users
            SET is_verified = true, email_verified_at = NOW(), updated_at = NOW()
            WHERE id = $1
        "#;

        sqlx::query(query)
            .bind(user_id)
            .execute(&self.pool)
            .await
            .map_err(|e| EmailVerificationError::DatabaseError(e.to_string()))?;

        Ok(())
    }
}
