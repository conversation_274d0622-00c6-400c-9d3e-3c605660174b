{"rustc": 15497389221046826682, "features": "[\"as_ref\", \"default\", \"deref\", \"deref_mut\", \"display\", \"error\", \"from\"]", "declared_features": "[\"add\", \"add_assign\", \"as_ref\", \"constructor\", \"debug\", \"default\", \"deref\", \"deref_mut\", \"display\", \"error\", \"from\", \"from_str\", \"full\", \"index\", \"index_mut\", \"into\", \"into_iterator\", \"is_variant\", \"mul\", \"mul_assign\", \"not\", \"sum\", \"testing-helpers\", \"try_from\", \"try_into\", \"try_unwrap\", \"unwrap\"]", "target": 11796376952621915773, "profile": 5373973626925704462, "path": 1337834246026705448, "deps": [[3060637413840920116, "proc_macro2", false, 15190708444295200371], [10640660562325816595, "syn", false, 16846682056436644373], [16126285161989458480, "unicode_xid", false, 10918294210963187098], [17990358020177143287, "quote", false, 3409982363884369376]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/derive_more-impl-777be430c963f892/dep-lib-derive_more_impl", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}