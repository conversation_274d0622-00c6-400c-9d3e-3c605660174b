{"rustc": 15497389221046826682, "features": "[\"http1\", \"http2\", \"ring\", \"tls12\", \"webpki-roots\", \"webpki-tokio\"]", "declared_features": "[\"aws-lc-rs\", \"default\", \"fips\", \"http1\", \"http2\", \"log\", \"logging\", \"native-tokio\", \"ring\", \"rustls-native-certs\", \"rustls-platform-verifier\", \"tls12\", \"webpki-roots\", \"webpki-tokio\"]", "target": 12220062926890100908, "profile": 8276155916380437441, "path": 17349890126053204355, "deps": [[778154619793643451, "hyper_util", false, 15792710683821140392], [784494742817713399, "tower_service", false, 2610997027074743787], [2883436298747778685, "pki_types", false, 1583994131520102190], [4942430025333810336, "webpki_roots", false, 11241219351129530914], [7161480121686072451, "rustls", false, 2439401492732675442], [9010263965687315507, "http", false, 7817754961798439998], [9538054652646069845, "tokio", false, 10671274603649774348], [11895591994124935963, "tokio_rustls", false, 1385708648473240595], [11957360342995674422, "hyper", false, 6887363626243675656]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/hyper-rustls-620858b3d49ba2ef/dep-lib-hyper_rustls", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}