{"rustc": 15497389221046826682, "features": "[\"alloc\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"debug\", \"default\", \"simd\", \"std\", \"unstable-doc\", \"unstable-recover\"]", "target": 13376497836617006023, "profile": 11259629673856435433, "path": 2019186296832334276, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/winnow-57d5e9a8448af1f9/dep-lib-winnow", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}