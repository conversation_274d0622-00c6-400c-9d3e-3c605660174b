{"rustc": 15497389221046826682, "features": "[\"_rt-tokio\", \"_tls-rustls-ring-webpki\", \"chrono\", \"default\", \"derive\", \"json\", \"macros\", \"migrate\", \"postgres\", \"sqlx-postgres\", \"tokio\", \"uuid\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_sqlite\", \"_tls-native-tls\", \"_tls-rustls-aws-lc-rs\", \"_tls-rustls-ring-native-roots\", \"_tls-rustls-ring-webpki\", \"async-std\", \"bigdecimal\", \"bit-vec\", \"chrono\", \"default\", \"derive\", \"ipnet\", \"ipnetwork\", \"json\", \"mac_address\", \"macros\", \"migrate\", \"mysql\", \"postgres\", \"rust_decimal\", \"sqlite\", \"sqlite-unbundled\", \"sqlx-mysql\", \"sqlx-postgres\", \"sqlx-sqlite\", \"time\", \"tokio\", \"uuid\"]", "target": 961973412475639632, "profile": 12675354917325698667, "path": 13145130718166573417, "deps": [[530211389790465181, "hex", false, 7175528866315000065], [3060637413840920116, "proc_macro2", false, 15190708444295200371], [3150220818285335163, "url", false, 12943689914781459339], [3405707034081185165, "dotenvy", false, 2603778432668016969], [3722963349756955755, "once_cell", false, 3887223149597883535], [3890005768372083955, "sqlx_postgres", false, 18014519910114343621], [9538054652646069845, "tokio", false, 14591110129000165097], [9689903380558560274, "serde", false, 7413703760361714852], [9857275760291862238, "sha2", false, 15317505335940927375], [10640660562325816595, "syn", false, 16846682056436644373], [10776111606377762245, "sqlx_core", false, 3796554591105587855], [12170264697963848012, "either", false, 12439185067536263909], [13077543566650298139, "heck", false, 1914459739501681260], [15367738274754116744, "serde_json", false, 5156170448367053435], [17990358020177143287, "quote", false, 3409982363884369376]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/sqlx-macros-core-25667512d608eabb/dep-lib-sqlx_macros_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}