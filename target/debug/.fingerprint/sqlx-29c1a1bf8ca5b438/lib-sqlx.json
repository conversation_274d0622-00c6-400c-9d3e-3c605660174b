{"rustc": 15497389221046826682, "features": "[\"_rt-tokio\", \"any\", \"chrono\", \"default\", \"derive\", \"json\", \"macros\", \"migrate\", \"postgres\", \"runtime-tokio\", \"runtime-tokio-rustls\", \"sqlx-macros\", \"sqlx-postgres\", \"tls-rustls-ring\", \"tls-rustls-ring-webpki\", \"uuid\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_sqlite\", \"_unstable-all-types\", \"all-databases\", \"any\", \"bigdecimal\", \"bit-vec\", \"bstr\", \"chrono\", \"default\", \"derive\", \"ipnet\", \"ipnetwork\", \"json\", \"mac_address\", \"macros\", \"migrate\", \"mysql\", \"postgres\", \"regexp\", \"runtime-async-std\", \"runtime-async-std-native-tls\", \"runtime-async-std-rustls\", \"runtime-tokio\", \"runtime-tokio-native-tls\", \"runtime-tokio-rustls\", \"rust_decimal\", \"sqlite\", \"sqlite-preupdate-hook\", \"sqlite-unbundled\", \"sqlx-macros\", \"sqlx-mysql\", \"sqlx-postgres\", \"sqlx-sqlite\", \"time\", \"tls-native-tls\", \"tls-none\", \"tls-rustls\", \"tls-rustls-aws-lc-rs\", \"tls-rustls-ring\", \"tls-rustls-ring-native-roots\", \"tls-rustls-ring-webpki\", \"uuid\"]", "target": 3003836824758849296, "profile": 8276155916380437441, "path": 12530861092551487082, "deps": [[3276107248499827220, "sqlx_macros", false, 1155841653024437770], [3890005768372083955, "sqlx_postgres", false, 3151854853689188538], [10776111606377762245, "sqlx_core", false, 12022233321188666335]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/sqlx-29c1a1bf8ca5b438/dep-lib-sqlx", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}