{"rustc": 15497389221046826682, "features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"charset\", \"default\", \"default-tls\", \"h2\", \"http2\", \"json\", \"native-tls\", \"rustls-tls\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"system-proxy\"]", "declared_features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"blocking\", \"brotli\", \"charset\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"gzip\", \"h2\", \"hickory-dns\", \"http2\", \"http3\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-manual-roots-no-provider\", \"rustls-tls-native-roots\", \"rustls-tls-native-roots-no-provider\", \"rustls-tls-no-provider\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"socks\", \"stream\", \"system-proxy\", \"trust-dns\", \"zstd\"]", "target": 8885864859914201979, "profile": 10900257523021023328, "path": 15909332533774717093, "deps": [[40386456601120721, "percent_encoding", false, 13653301289869840339], [418947936956741439, "h2", false, 9295156471981602873], [778154619793643451, "hyper_util", false, 15792710683821140392], [784494742817713399, "tower_service", false, 2610997027074743787], [1788832197870803419, "hyper_rustls", false, 12625184291852262713], [1906322745568073236, "pin_project_lite", false, 1061008319237945008], [2054153378684941554, "tower_http", false, 14585315369212989466], [2517136641825875337, "sync_wrapper", false, 9312011713707350723], [2883436298747778685, "rustls_pki_types", false, 1583994131520102190], [3150220818285335163, "url", false, 12921086093556899121], [4942430025333810336, "webpki_roots", false, 11241219351129530914], [5695049318159433696, "tower", false, 17066016548994518545], [5986029879202738730, "log", false, 11319510212737816312], [7161480121686072451, "rustls", false, 2439401492732675442], [7620660491849607393, "futures_core", false, 5863991803937352255], [9010263965687315507, "http", false, 7817754961798439998], [9538054652646069845, "tokio", false, 10671274603649774348], [9689903380558560274, "serde", false, 15661745430208460648], [10229185211513642314, "mime", false, 6495140202071976503], [11895591994124935963, "tokio_rustls", false, 1385708648473240595], [11957360342995674422, "hyper", false, 6887363626243675656], [12186126227181294540, "tokio_native_tls", false, 588344324206899192], [13077212702700853852, "base64", false, 13193332678885186892], [14084095096285906100, "http_body", false, 6415933620002814850], [14564311161534545801, "encoding_rs", false, 1243290793641145543], [15367738274754116744, "serde_json", false, 12562571082948322691], [16066129441945555748, "bytes", false, 16365441259322957946], [16542808166767769916, "serde_urlencoded", false, 7590451246847570889], [16785601910559813697, "native_tls_crate", false, 1671933830624978433], [16900715236047033623, "http_body_util", false, 9149268528455045754], [18273243456331255970, "hyper_tls", false, 18401207131347107343]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/reqwest-0f309aabdcc4eb7f/dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}