{"$message_type":"diagnostic","message":"expected identifier, found reserved keyword `gen`","code":null,"level":"error","spans":[{"file_name":"src/services/mfa_service.rs","byte_start":2154,"byte_end":2157,"line_start":73,"line_end":73,"column_start":57,"column_end":60,"is_primary":true,"text":[{"text":"        let secret_bytes: Vec<u8> = (0..20).map(|_| rng.gen()).collect();","highlight_start":57,"highlight_end":60}],"label":"expected identifier, found reserved keyword","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"escape `gen` to use it as an identifier","code":null,"level":"help","spans":[{"file_name":"src/services/mfa_service.rs","byte_start":2154,"byte_end":2154,"line_start":73,"line_end":73,"column_start":57,"column_end":57,"is_primary":true,"text":[{"text":"        let secret_bytes: Vec<u8> = (0..20).map(|_| rng.gen()).collect();","highlight_start":57,"highlight_end":57}],"label":null,"suggested_replacement":"r#","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: expected identifier, found reserved keyword `gen`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/mfa_service.rs:73:57\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m73\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let secret_bytes: Vec<u8> = (0..20).map(|_| rng.gen()).collect();\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected identifier, found reserved keyword\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: escape `gen` to use it as an identifier\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m73\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0m        let secret_bytes: Vec<u8> = (0..20).map(|_| rng.\u001b[0m\u001b[0m\u001b[38;5;10mr#\u001b[0m\u001b[0mgen()).collect();\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                         \u001b[0m\u001b[0m\u001b[38;5;10m++\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"expected identifier, found reserved keyword `gen`","code":null,"level":"error","spans":[{"file_name":"src/services/email_verification_service.rs","byte_start":7417,"byte_end":7420,"line_start":214,"line_end":214,"column_start":35,"column_end":38,"is_primary":true,"text":[{"text":"        let bytes: [u8; 32] = rng.gen();","highlight_start":35,"highlight_end":38}],"label":"expected identifier, found reserved keyword","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"escape `gen` to use it as an identifier","code":null,"level":"help","spans":[{"file_name":"src/services/email_verification_service.rs","byte_start":7417,"byte_end":7417,"line_start":214,"line_end":214,"column_start":35,"column_end":35,"is_primary":true,"text":[{"text":"        let bytes: [u8; 32] = rng.gen();","highlight_start":35,"highlight_end":35}],"label":null,"suggested_replacement":"r#","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: expected identifier, found reserved keyword `gen`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/email_verification_service.rs:214:35\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m214\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let bytes: [u8; 32] = rng.gen();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected identifier, found reserved keyword\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: escape `gen` to use it as an identifier\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m214\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0m        let bytes: [u8; 32] = rng.\u001b[0m\u001b[0m\u001b[38;5;10mr#\u001b[0m\u001b[0mgen();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                   \u001b[0m\u001b[0m\u001b[38;5;10m++\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"set `DATABASE_URL` to use query macros online, or run `cargo sqlx prepare` to update the query cache","code":null,"level":"error","spans":[{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-0.8.6/src/macros/mod.rs","byte_start":16544,"byte_end":16615,"line_start":328,"line_end":328,"column_start":9,"column_end":80,"is_primary":true,"text":[{"text":"        $crate::sqlx_macros::expand_query!(source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":80}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-0.8.6/src/macros/mod.rs","byte_start":16544,"byte_end":16615,"line_start":328,"line_end":328,"column_start":9,"column_end":80,"is_primary":false,"text":[{"text":"        $crate::sqlx_macros::expand_query!(source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":80}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src/services/oauth_service.rs","byte_start":8846,"byte_end":9006,"line_start":239,"line_end":243,"column_start":22,"column_end":10,"is_primary":false,"text":[{"text":"        let result = sqlx::query!(","highlight_start":22,"highlight_end":35},{"text":"            \"DELETE FROM oauth_accounts WHERE user_id = $1 AND provider = $2\",","highlight_start":1,"highlight_end":79},{"text":"            request.user_id,","highlight_start":1,"highlight_end":29},{"text":"            request.provider","highlight_start":1,"highlight_end":29},{"text":"        )","highlight_start":1,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"sqlx::query!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-0.8.6/src/macros/mod.rs","byte_start":15636,"byte_end":15654,"line_start":314,"line_end":314,"column_start":1,"column_end":19,"is_primary":false,"text":[{"text":"macro_rules! query (","highlight_start":1,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::sqlx_macros::expand_query!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-macros-0.8.6/src/lib.rs","byte_start":116,"byte_end":170,"line_start":9,"line_end":9,"column_start":1,"column_end":55,"is_primary":false,"text":[{"text":"pub fn expand_query(input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: set `DATABASE_URL` to use query macros online, or run `cargo sqlx prepare` to update the query cache\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/oauth_service.rs:239:22\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m239\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m        let result = sqlx::query!(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m ______________________^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m240\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            \"DELETE FROM oauth_accounts WHERE user_id = $1 AND provider = $2\",\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m241\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            request.user_id,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m242\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            request.provider\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m243\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        )\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_________^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"set `DATABASE_URL` to use query macros online, or run `cargo sqlx prepare` to update the query cache","code":null,"level":"error","spans":[{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-0.8.6/src/macros/mod.rs","byte_start":16544,"byte_end":16615,"line_start":328,"line_end":328,"column_start":9,"column_end":80,"is_primary":true,"text":[{"text":"        $crate::sqlx_macros::expand_query!(source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":80}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-0.8.6/src/macros/mod.rs","byte_start":16544,"byte_end":16615,"line_start":328,"line_end":328,"column_start":9,"column_end":80,"is_primary":false,"text":[{"text":"        $crate::sqlx_macros::expand_query!(source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":80}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src/services/oauth_service.rs","byte_start":10493,"byte_end":10760,"line_start":290,"line_end":297,"column_start":9,"column_end":10,"is_primary":false,"text":[{"text":"        sqlx::query!(","highlight_start":9,"highlight_end":22},{"text":"            \"INSERT INTO oauth_states (state, code_verifier, redirect_uri, provider, expires_at) VALUES ($1, $2, $3, $4, $5)\",","highlight_start":1,"highlight_end":127},{"text":"            state,","highlight_start":1,"highlight_end":19},{"text":"            code_verifier,","highlight_start":1,"highlight_end":27},{"text":"            redirect_uri,","highlight_start":1,"highlight_end":26},{"text":"            provider,","highlight_start":1,"highlight_end":22},{"text":"            expires_at","highlight_start":1,"highlight_end":23},{"text":"        )","highlight_start":1,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"sqlx::query!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-0.8.6/src/macros/mod.rs","byte_start":15636,"byte_end":15654,"line_start":314,"line_end":314,"column_start":1,"column_end":19,"is_primary":false,"text":[{"text":"macro_rules! query (","highlight_start":1,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::sqlx_macros::expand_query!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-macros-0.8.6/src/lib.rs","byte_start":116,"byte_end":170,"line_start":9,"line_end":9,"column_start":1,"column_end":55,"is_primary":false,"text":[{"text":"pub fn expand_query(input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: set `DATABASE_URL` to use query macros online, or run `cargo sqlx prepare` to update the query cache\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/oauth_service.rs:290:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m290\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m        sqlx::query!(\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m291\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            \"INSERT INTO oauth_states (state, code_verifier, redirect_uri, provider, expires_at) VALUES ($1, $2, $3, $4, $5)\",\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m292\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            state,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m293\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            code_verifier,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m296\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            expires_at\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m297\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        )\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_________^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"set `DATABASE_URL` to use query macros online, or run `cargo sqlx prepare` to update the query cache","code":null,"level":"error","spans":[{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-0.8.6/src/macros/mod.rs","byte_start":16544,"byte_end":16615,"line_start":328,"line_end":328,"column_start":9,"column_end":80,"is_primary":true,"text":[{"text":"        $crate::sqlx_macros::expand_query!(source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":80}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-0.8.6/src/macros/mod.rs","byte_start":16544,"byte_end":16615,"line_start":328,"line_end":328,"column_start":9,"column_end":80,"is_primary":false,"text":[{"text":"        $crate::sqlx_macros::expand_query!(source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":80}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src/services/oauth_service.rs","byte_start":10933,"byte_end":11127,"line_start":305,"line_end":308,"column_start":19,"column_end":10,"is_primary":false,"text":[{"text":"        let row = sqlx::query!(","highlight_start":19,"highlight_end":32},{"text":"            \"SELECT id, state, code_verifier, redirect_uri, provider, expires_at, created_at FROM oauth_states WHERE state = $1 AND expires_at > NOW()\",","highlight_start":1,"highlight_end":153},{"text":"            state","highlight_start":1,"highlight_end":18},{"text":"        )","highlight_start":1,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"sqlx::query!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-0.8.6/src/macros/mod.rs","byte_start":15636,"byte_end":15654,"line_start":314,"line_end":314,"column_start":1,"column_end":19,"is_primary":false,"text":[{"text":"macro_rules! query (","highlight_start":1,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::sqlx_macros::expand_query!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-macros-0.8.6/src/lib.rs","byte_start":116,"byte_end":170,"line_start":9,"line_end":9,"column_start":1,"column_end":55,"is_primary":false,"text":[{"text":"pub fn expand_query(input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: set `DATABASE_URL` to use query macros online, or run `cargo sqlx prepare` to update the query cache\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/oauth_service.rs:305:19\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m305\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m        let row = sqlx::query!(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m ___________________^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m306\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            \"SELECT id, state, code_verifier, redirect_uri, provider, expires_at, created_at FROM oauth_states WHERE state = $1 AND expires_at > NOW()\",\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m307\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            state\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m308\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        )\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_________^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"set `DATABASE_URL` to use query macros online, or run `cargo sqlx prepare` to update the query cache","code":null,"level":"error","spans":[{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-0.8.6/src/macros/mod.rs","byte_start":16544,"byte_end":16615,"line_start":328,"line_end":328,"column_start":9,"column_end":80,"is_primary":true,"text":[{"text":"        $crate::sqlx_macros::expand_query!(source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":80}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-0.8.6/src/macros/mod.rs","byte_start":16544,"byte_end":16615,"line_start":328,"line_end":328,"column_start":9,"column_end":80,"is_primary":false,"text":[{"text":"        $crate::sqlx_macros::expand_query!(source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":80}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src/services/oauth_service.rs","byte_start":11644,"byte_end":11708,"line_start":325,"line_end":325,"column_start":9,"column_end":73,"is_primary":false,"text":[{"text":"        sqlx::query!(\"DELETE FROM oauth_states WHERE state = $1\", state)","highlight_start":9,"highlight_end":73}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"sqlx::query!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-0.8.6/src/macros/mod.rs","byte_start":15636,"byte_end":15654,"line_start":314,"line_end":314,"column_start":1,"column_end":19,"is_primary":false,"text":[{"text":"macro_rules! query (","highlight_start":1,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::sqlx_macros::expand_query!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-macros-0.8.6/src/lib.rs","byte_start":116,"byte_end":170,"line_start":9,"line_end":9,"column_start":1,"column_end":55,"is_primary":false,"text":[{"text":"pub fn expand_query(input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: set `DATABASE_URL` to use query macros online, or run `cargo sqlx prepare` to update the query cache\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/oauth_service.rs:325:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m325\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        sqlx::query!(\"DELETE FROM oauth_states WHERE state = $1\", state)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"set `DATABASE_URL` to use query macros online, or run `cargo sqlx prepare` to update the query cache","code":null,"level":"error","spans":[{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-0.8.6/src/macros/mod.rs","byte_start":16544,"byte_end":16615,"line_start":328,"line_end":328,"column_start":9,"column_end":80,"is_primary":true,"text":[{"text":"        $crate::sqlx_macros::expand_query!(source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":80}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-0.8.6/src/macros/mod.rs","byte_start":16544,"byte_end":16615,"line_start":328,"line_end":328,"column_start":9,"column_end":80,"is_primary":false,"text":[{"text":"        $crate::sqlx_macros::expand_query!(source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":80}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src/services/oauth_service.rs","byte_start":11962,"byte_end":12236,"line_start":336,"line_end":340,"column_start":19,"column_end":10,"is_primary":false,"text":[{"text":"        let row = sqlx::query!(","highlight_start":19,"highlight_end":32},{"text":"            \"SELECT id, user_id, provider, provider_user_id, email, access_token, refresh_token, expires_at, created_at, updated_at FROM oauth_accounts WHERE provider = $1 AND provider_user_id = $2\",","highlight_start":1,"highlight_end":200},{"text":"            provider,","highlight_start":1,"highlight_end":22},{"text":"            provider_user_id","highlight_start":1,"highlight_end":29},{"text":"        )","highlight_start":1,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"sqlx::query!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-0.8.6/src/macros/mod.rs","byte_start":15636,"byte_end":15654,"line_start":314,"line_end":314,"column_start":1,"column_end":19,"is_primary":false,"text":[{"text":"macro_rules! query (","highlight_start":1,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::sqlx_macros::expand_query!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-macros-0.8.6/src/lib.rs","byte_start":116,"byte_end":170,"line_start":9,"line_end":9,"column_start":1,"column_end":55,"is_primary":false,"text":[{"text":"pub fn expand_query(input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: set `DATABASE_URL` to use query macros online, or run `cargo sqlx prepare` to update the query cache\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/oauth_service.rs:336:19\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m336\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m        let row = sqlx::query!(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m ___________________^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m337\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            \"SELECT id, user_id, provider, provider_user_id, email, access_token, refresh_token, expires_at, created_at, updated_at FROM oauth_accounts WHERE provider = $1 AND provider_user_id = $2\",\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m338\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            provider,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m339\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            provider_user_id\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m340\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        )\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_________^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"set `DATABASE_URL` to use query macros online, or run `cargo sqlx prepare` to update the query cache","code":null,"level":"error","spans":[{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-0.8.6/src/macros/mod.rs","byte_start":16544,"byte_end":16615,"line_start":328,"line_end":328,"column_start":9,"column_end":80,"is_primary":true,"text":[{"text":"        $crate::sqlx_macros::expand_query!(source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":80}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-0.8.6/src/macros/mod.rs","byte_start":16544,"byte_end":16615,"line_start":328,"line_end":328,"column_start":9,"column_end":80,"is_primary":false,"text":[{"text":"        $crate::sqlx_macros::expand_query!(source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":80}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src/services/oauth_service.rs","byte_start":13179,"byte_end":13709,"line_start":370,"line_end":383,"column_start":19,"column_end":10,"is_primary":false,"text":[{"text":"        let row = sqlx::query!(","highlight_start":19,"highlight_end":32},{"text":"            r#\"","highlight_start":1,"highlight_end":16},{"text":"            INSERT INTO oauth_accounts (user_id, provider, provider_user_id, email, access_token, refresh_token, expires_at)","highlight_start":1,"highlight_end":125},{"text":"            VALUES ($1, $2, $3, $4, $5, $6, $7)","highlight_start":1,"highlight_end":48},{"text":"            RETURNING id, user_id, provider, provider_user_id, email, access_token, refresh_token, expires_at, created_at, updated_at","highlight_start":1,"highlight_end":134},{"text":"            \"#,","highlight_start":1,"highlight_end":16},{"text":"            user_id,","highlight_start":1,"highlight_end":21},{"text":"            provider,","highlight_start":1,"highlight_end":22},{"text":"            provider_user_id,","highlight_start":1,"highlight_end":30},{"text":"            email,","highlight_start":1,"highlight_end":19},{"text":"            access_token,","highlight_start":1,"highlight_end":26},{"text":"            refresh_token,","highlight_start":1,"highlight_end":27},{"text":"            expires_at","highlight_start":1,"highlight_end":23},{"text":"        )","highlight_start":1,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"sqlx::query!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-0.8.6/src/macros/mod.rs","byte_start":15636,"byte_end":15654,"line_start":314,"line_end":314,"column_start":1,"column_end":19,"is_primary":false,"text":[{"text":"macro_rules! query (","highlight_start":1,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::sqlx_macros::expand_query!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-macros-0.8.6/src/lib.rs","byte_start":116,"byte_end":170,"line_start":9,"line_end":9,"column_start":1,"column_end":55,"is_primary":false,"text":[{"text":"pub fn expand_query(input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: set `DATABASE_URL` to use query macros online, or run `cargo sqlx prepare` to update the query cache\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/oauth_service.rs:370:19\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m370\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m        let row = sqlx::query!(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m ___________________^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m371\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            r#\"\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m372\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            INSERT INTO oauth_accounts (user_id, provider, provider_user_id, email, access_token, refresh_token, expires_at)\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m373\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            VALUES ($1, $2, $3, $4, $5, $6, $7)\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m382\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            expires_at\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m383\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        )\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_________^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"set `DATABASE_URL` to use query macros online, or run `cargo sqlx prepare` to update the query cache","code":null,"level":"error","spans":[{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-0.8.6/src/macros/mod.rs","byte_start":16544,"byte_end":16615,"line_start":328,"line_end":328,"column_start":9,"column_end":80,"is_primary":true,"text":[{"text":"        $crate::sqlx_macros::expand_query!(source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":80}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-0.8.6/src/macros/mod.rs","byte_start":16544,"byte_end":16615,"line_start":328,"line_end":328,"column_start":9,"column_end":80,"is_primary":false,"text":[{"text":"        $crate::sqlx_macros::expand_query!(source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":80}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src/services/oauth_service.rs","byte_start":14563,"byte_end":14822,"line_start":410,"line_end":416,"column_start":9,"column_end":10,"is_primary":false,"text":[{"text":"        sqlx::query!(","highlight_start":9,"highlight_end":22},{"text":"            \"UPDATE oauth_accounts SET access_token = $1, refresh_token = $2, expires_at = $3, updated_at = NOW() WHERE id = $4\",","highlight_start":1,"highlight_end":130},{"text":"            access_token,","highlight_start":1,"highlight_end":26},{"text":"            refresh_token,","highlight_start":1,"highlight_end":27},{"text":"            expires_at,","highlight_start":1,"highlight_end":24},{"text":"            oauth_account_id","highlight_start":1,"highlight_end":29},{"text":"        )","highlight_start":1,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"sqlx::query!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-0.8.6/src/macros/mod.rs","byte_start":15636,"byte_end":15654,"line_start":314,"line_end":314,"column_start":1,"column_end":19,"is_primary":false,"text":[{"text":"macro_rules! query (","highlight_start":1,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::sqlx_macros::expand_query!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/sqlx-macros-0.8.6/src/lib.rs","byte_start":116,"byte_end":170,"line_start":9,"line_end":9,"column_start":1,"column_end":55,"is_primary":false,"text":[{"text":"pub fn expand_query(input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: set `DATABASE_URL` to use query macros online, or run `cargo sqlx prepare` to update the query cache\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/oauth_service.rs:410:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m410\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m        sqlx::query!(\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m411\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            \"UPDATE oauth_accounts SET access_token = $1, refresh_token = $2, expires_at = $3, updated_at = NOW() WHERE id = $4\",\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m412\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            access_token,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m413\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            refresh_token,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m414\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            expires_at,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m415\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            oauth_account_id\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m416\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        )\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_________^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unresolved import `oauth2::reqwest::async_http_client`","code":{"code":"E0432","explanation":"An import was unresolved.\n\nErroneous code example:\n\n```compile_fail,E0432\nuse something::Foo; // error: unresolved import `something::Foo`.\n```\n\nIn Rust 2015, paths in `use` statements are relative to the crate root. To\nimport items relative to the current and parent modules, use the `self::` and\n`super::` prefixes, respectively.\n\nIn Rust 2018 or later, paths in `use` statements are relative to the current\nmodule unless they begin with the name of a crate or a literal `crate::`, in\nwhich case they start from the crate root. As in Rust 2015 code, the `self::`\nand `super::` prefixes refer to the current and parent modules respectively.\n\nAlso verify that you didn't misspell the import name and that the import exists\nin the module from where you tried to import it. Example:\n\n```\nuse self::something::Foo; // Ok.\n\nmod something {\n    pub struct Foo;\n}\n# fn main() {}\n```\n\nIf you tried to use a module from an external crate and are using Rust 2015,\nyou may have missed the `extern crate` declaration (which is usually placed in\nthe crate root):\n\n```edition2015\nextern crate core; // Required to use the `core` crate in Rust 2015.\n\nuse core::any;\n# fn main() {}\n```\n\nSince Rust 2018 the `extern crate` declaration is not required and\nyou can instead just `use` it:\n\n```edition2018\nuse core::any; // No extern crate required in Rust 2018.\n# fn main() {}\n```\n"},"level":"error","spans":[{"file_name":"src/services/oauth_service.rs","byte_start":17360,"byte_end":17394,"line_start":480,"line_end":480,"column_start":5,"column_end":39,"is_primary":true,"text":[{"text":"use oauth2::reqwest::async_http_client;","highlight_start":5,"highlight_end":39}],"label":"no `async_http_client` in the root","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0432]\u001b[0m\u001b[0m\u001b[1m: unresolved import `oauth2::reqwest::async_http_client`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/oauth_service.rs:480:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m480\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse oauth2::reqwest::async_http_client;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mno `async_http_client` in the root\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unresolved import `rand::distributions`","code":{"code":"E0432","explanation":"An import was unresolved.\n\nErroneous code example:\n\n```compile_fail,E0432\nuse something::Foo; // error: unresolved import `something::Foo`.\n```\n\nIn Rust 2015, paths in `use` statements are relative to the crate root. To\nimport items relative to the current and parent modules, use the `self::` and\n`super::` prefixes, respectively.\n\nIn Rust 2018 or later, paths in `use` statements are relative to the current\nmodule unless they begin with the name of a crate or a literal `crate::`, in\nwhich case they start from the crate root. As in Rust 2015 code, the `self::`\nand `super::` prefixes refer to the current and parent modules respectively.\n\nAlso verify that you didn't misspell the import name and that the import exists\nin the module from where you tried to import it. Example:\n\n```\nuse self::something::Foo; // Ok.\n\nmod something {\n    pub struct Foo;\n}\n# fn main() {}\n```\n\nIf you tried to use a module from an external crate and are using Rust 2015,\nyou may have missed the `extern crate` declaration (which is usually placed in\nthe crate root):\n\n```edition2015\nextern crate core; // Required to use the `core` crate in Rust 2015.\n\nuse core::any;\n# fn main() {}\n```\n\nSince Rust 2018 the `extern crate` declaration is not required and\nyou can instead just `use` it:\n\n```edition2018\nuse core::any; // No extern crate required in Rust 2018.\n# fn main() {}\n```\n"},"level":"error","spans":[{"file_name":"src/utils/crypto.rs","byte_start":39,"byte_end":52,"line_start":2,"line_end":2,"column_start":11,"column_end":24,"is_primary":true,"text":[{"text":"use rand::distributions::Alphanumeric;","highlight_start":11,"highlight_end":24}],"label":"could not find `distributions` in `rand`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0432]\u001b[0m\u001b[0m\u001b[1m: unresolved import `rand::distributions`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/utils/crypto.rs:2:11\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse rand::distributions::Alphanumeric;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcould not find `distributions` in `rand`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"failed to resolve: use of unresolved module or unlinked crate `config`","code":{"code":"E0433","explanation":"An undeclared crate, module, or type was used.\n\nErroneous code example:\n\n```compile_fail,E0433\nlet map = HashMap::new();\n// error: failed to resolve: use of undeclared type `HashMap`\n```\n\nPlease verify you didn't misspell the type/module's name or that you didn't\nforget to import it:\n\n```\nuse std::collections::HashMap; // HashMap has been imported.\nlet map: HashMap<u32, u32> = HashMap::new(); // So it can be used!\n```\n\nIf you've expected to use a crate name:\n\n```compile_fail\nuse ferris_wheel::BigO;\n// error: failed to resolve: use of undeclared module or unlinked crate\n```\n\nMake sure the crate has been added as a dependency in `Cargo.toml`.\n\nTo use a module from your current crate, add the `crate::` prefix to the path.\n"},"level":"error","spans":[{"file_name":"src/config/mod.rs","byte_start":3397,"byte_end":3403,"line_start":105,"line_end":105,"column_start":13,"column_end":19,"is_primary":true,"text":[{"text":"            config::Environment::default()","highlight_start":13,"highlight_end":19}],"label":"use of unresolved module or unlinked crate `config`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if you wanted to use a crate named `config`, use `cargo add config` to add it to your `Cargo.toml`","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0433]\u001b[0m\u001b[0m\u001b[1m: failed to resolve: use of unresolved module or unlinked crate `config`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/config/mod.rs:105:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m105\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            config::Environment::default()\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9muse of unresolved module or unlinked crate `config`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: if you wanted to use a crate named `config`, use `cargo add config` to add it to your `Cargo.toml`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"failed to resolve: use of unresolved module or unlinked crate `config`","code":{"code":"E0433","explanation":"An undeclared crate, module, or type was used.\n\nErroneous code example:\n\n```compile_fail,E0433\nlet map = HashMap::new();\n// error: failed to resolve: use of undeclared type `HashMap`\n```\n\nPlease verify you didn't misspell the type/module's name or that you didn't\nforget to import it:\n\n```\nuse std::collections::HashMap; // HashMap has been imported.\nlet map: HashMap<u32, u32> = HashMap::new(); // So it can be used!\n```\n\nIf you've expected to use a crate name:\n\n```compile_fail\nuse ferris_wheel::BigO;\n// error: failed to resolve: use of undeclared module or unlinked crate\n```\n\nMake sure the crate has been added as a dependency in `Cargo.toml`.\n\nTo use a module from your current crate, add the `crate::` prefix to the path.\n"},"level":"error","spans":[{"file_name":"src/config/mod.rs","byte_start":1638,"byte_end":1644,"line_start":71,"line_end":71,"column_start":23,"column_end":29,"is_primary":true,"text":[{"text":"        let mut cfg = config::Config::builder()","highlight_start":23,"highlight_end":29}],"label":"use of unresolved module or unlinked crate `config`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if you wanted to use a crate named `config`, use `cargo add config` to add it to your `Cargo.toml`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"consider importing one of these items","code":null,"level":"help","spans":[{"file_name":"src/config/mod.rs","byte_start":0,"byte_end":0,"line_start":1,"line_end":1,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use serde::Deserialize;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"use crate::Config;\n","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"src/config/mod.rs","byte_start":0,"byte_end":0,"line_start":1,"line_end":1,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use serde::Deserialize;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"use actix_web_httpauth::extractors::basic::Config;\n","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"src/config/mod.rs","byte_start":0,"byte_end":0,"line_start":1,"line_end":1,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use serde::Deserialize;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"use actix_web_httpauth::extractors::bearer::Config;\n","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"src/config/mod.rs","byte_start":0,"byte_end":0,"line_start":1,"line_end":1,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use serde::Deserialize;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"use base64::engine::Config;\n","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null},{"message":"if you import `Config`, refer to it directly","code":null,"level":"help","spans":[{"file_name":"src/config/mod.rs","byte_start":1638,"byte_end":1646,"line_start":71,"line_end":71,"column_start":23,"column_end":31,"is_primary":true,"text":[{"text":"        let mut cfg = config::Config::builder()","highlight_start":23,"highlight_end":31}],"label":null,"suggested_replacement":"","suggestion_applicability":"Unspecified","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0433]\u001b[0m\u001b[0m\u001b[1m: failed to resolve: use of unresolved module or unlinked crate `config`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/config/mod.rs:71:23\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m71\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut cfg = config::Config::builder()\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9muse of unresolved module or unlinked crate `config`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: if you wanted to use a crate named `config`, use `cargo add config` to add it to your `Cargo.toml`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: consider importing one of these items\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[38;5;10m+ use crate::Config;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[38;5;10m+ use actix_web_httpauth::extractors::basic::Config;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[38;5;10m+ use actix_web_httpauth::extractors::bearer::Config;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[38;5;10m+ use base64::engine::Config;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: if you import `Config`, refer to it directly\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m71\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        let mut cfg = \u001b[0m\u001b[0m\u001b[38;5;9mconfig::\u001b[0m\u001b[0mConfig::builder()\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m71\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        let mut cfg = Config::builder()\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `std::collections::HashMap`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/config/mod.rs","byte_start":28,"byte_end":53,"line_start":2,"line_end":2,"column_start":5,"column_end":30,"is_primary":true,"text":[{"text":"use std::collections::HashMap;","highlight_start":5,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/config/mod.rs","byte_start":24,"byte_end":55,"line_start":2,"line_end":3,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::collections::HashMap;","highlight_start":1,"highlight_end":31},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `std::collections::HashMap`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/config/mod.rs:2:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::collections::HashMap;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `uuid::Uuid`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/config/multi_app.rs","byte_start":97,"byte_end":107,"line_start":4,"line_end":4,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"use uuid::Uuid;","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/config/multi_app.rs","byte_start":93,"byte_end":109,"line_start":4,"line_end":5,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use uuid::Uuid;","highlight_start":1,"highlight_end":16},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `uuid::Uuid`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/config/multi_app.rs:4:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse uuid::Uuid;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `LoginRequest`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/handlers/auth.rs","byte_start":230,"byte_end":242,"line_start":6,"line_end":6,"column_start":40,"column_end":52,"is_primary":true,"text":[{"text":"use crate::models::{CreateUserRequest, LoginRequest};","highlight_start":40,"highlight_end":52}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/handlers/auth.rs","byte_start":228,"byte_end":242,"line_start":6,"line_end":6,"column_start":38,"column_end":52,"is_primary":true,"text":[{"text":"use crate::models::{CreateUserRequest, LoginRequest};","highlight_start":38,"highlight_end":52}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/handlers/auth.rs","byte_start":210,"byte_end":211,"line_start":6,"line_end":6,"column_start":20,"column_end":21,"is_primary":true,"text":[{"text":"use crate::models::{CreateUserRequest, LoginRequest};","highlight_start":20,"highlight_end":21}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/handlers/auth.rs","byte_start":242,"byte_end":243,"line_start":6,"line_end":6,"column_start":52,"column_end":53,"is_primary":true,"text":[{"text":"use crate::models::{CreateUserRequest, LoginRequest};","highlight_start":52,"highlight_end":53}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `LoginRequest`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/handlers/auth.rs:6:40\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::models::{CreateUserRequest, LoginRequest};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `GenerateBackupCodesResponse` and `MfaStatusResponse`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/handlers/mfa.rs","byte_start":238,"byte_end":255,"line_start":9,"line_end":9,"column_start":5,"column_end":22,"is_primary":true,"text":[{"text":"    MfaStatusResponse, GenerateBackupCodesResponse","highlight_start":5,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/handlers/mfa.rs","byte_start":257,"byte_end":284,"line_start":9,"line_end":9,"column_start":24,"column_end":51,"is_primary":true,"text":[{"text":"    MfaStatusResponse, GenerateBackupCodesResponse","highlight_start":24,"highlight_end":51}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src/handlers/mfa.rs","byte_start":232,"byte_end":284,"line_start":8,"line_end":9,"column_start":61,"column_end":51,"is_primary":true,"text":[{"text":"    MfaSetupRequest, MfaVerifySetupRequest, MfaVerifyRequest,","highlight_start":61,"highlight_end":62},{"text":"    MfaStatusResponse, GenerateBackupCodesResponse","highlight_start":1,"highlight_end":51}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `GenerateBackupCodesResponse` and `MfaStatusResponse`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/handlers/mfa.rs:9:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    MfaStatusResponse, GenerateBackupCodesResponse\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `warn`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/handlers/rbac.rs","byte_start":336,"byte_end":340,"line_start":9,"line_end":9,"column_start":28,"column_end":32,"is_primary":true,"text":[{"text":"use tracing::{error, info, warn};","highlight_start":28,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/handlers/rbac.rs","byte_start":334,"byte_end":340,"line_start":9,"line_end":9,"column_start":26,"column_end":32,"is_primary":true,"text":[{"text":"use tracing::{error, info, warn};","highlight_start":26,"highlight_end":32}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `warn`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/handlers/rbac.rs:9:28\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tracing::{error, info, warn};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `crate::utils::errors::AppError`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/handlers/email_verification.rs","byte_start":331,"byte_end":361,"line_start":9,"line_end":9,"column_start":5,"column_end":35,"is_primary":true,"text":[{"text":"use crate::utils::errors::AppError;","highlight_start":5,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/handlers/email_verification.rs","byte_start":327,"byte_end":363,"line_start":9,"line_end":10,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use crate::utils::errors::AppError;","highlight_start":1,"highlight_end":36},{"text":"use crate::middleware::rate_limiting::RateLimitingMiddleware;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `crate::utils::errors::AppError`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/handlers/email_verification.rs:9:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::utils::errors::AppError;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `crate::middleware::rate_limiting::RateLimitingMiddleware`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/handlers/email_verification.rs","byte_start":367,"byte_end":423,"line_start":10,"line_end":10,"column_start":5,"column_end":61,"is_primary":true,"text":[{"text":"use crate::middleware::rate_limiting::RateLimitingMiddleware;","highlight_start":5,"highlight_end":61}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/handlers/email_verification.rs","byte_start":363,"byte_end":425,"line_start":10,"line_end":11,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use crate::middleware::rate_limiting::RateLimitingMiddleware;","highlight_start":1,"highlight_end":62},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `crate::middleware::rate_limiting::RateLimitingMiddleware`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/handlers/email_verification.rs:10:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::middleware::rate_limiting::RateLimitingMiddleware;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `HttpRequest`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/handlers/oauth.rs","byte_start":21,"byte_end":32,"line_start":1,"line_end":1,"column_start":22,"column_end":33,"is_primary":true,"text":[{"text":"use actix_web::{web, HttpRequest, HttpResponse, Result as ActixResult};","highlight_start":22,"highlight_end":33}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/handlers/oauth.rs","byte_start":19,"byte_end":32,"line_start":1,"line_end":1,"column_start":20,"column_end":33,"is_primary":true,"text":[{"text":"use actix_web::{web, HttpRequest, HttpResponse, Result as ActixResult};","highlight_start":20,"highlight_end":33}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `HttpRequest`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/handlers/oauth.rs:1:22\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse actix_web::{web, HttpRequest, HttpResponse, Result as ActixResult};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `error` and `warn`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/handlers/tenant_handlers.rs","byte_start":293,"byte_end":297,"line_start":9,"line_end":9,"column_start":21,"column_end":25,"is_primary":true,"text":[{"text":"use tracing::{info, warn, error, instrument};","highlight_start":21,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/handlers/tenant_handlers.rs","byte_start":299,"byte_end":304,"line_start":9,"line_end":9,"column_start":27,"column_end":32,"is_primary":true,"text":[{"text":"use tracing::{info, warn, error, instrument};","highlight_start":27,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src/handlers/tenant_handlers.rs","byte_start":291,"byte_end":304,"line_start":9,"line_end":9,"column_start":19,"column_end":32,"is_primary":true,"text":[{"text":"use tracing::{info, warn, error, instrument};","highlight_start":19,"highlight_end":32}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `error` and `warn`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/handlers/tenant_handlers.rs:9:21\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tracing::{info, warn, error, instrument};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `error` and `warn`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/handlers/service_client_handlers.rs","byte_start":314,"byte_end":318,"line_start":9,"line_end":9,"column_start":21,"column_end":25,"is_primary":true,"text":[{"text":"use tracing::{info, warn, error, instrument};","highlight_start":21,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/handlers/service_client_handlers.rs","byte_start":320,"byte_end":325,"line_start":9,"line_end":9,"column_start":27,"column_end":32,"is_primary":true,"text":[{"text":"use tracing::{info, warn, error, instrument};","highlight_start":27,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src/handlers/service_client_handlers.rs","byte_start":312,"byte_end":325,"line_start":9,"line_end":9,"column_start":19,"column_end":32,"is_primary":true,"text":[{"text":"use tracing::{info, warn, error, instrument};","highlight_start":19,"highlight_end":32}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `error` and `warn`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/handlers/service_client_handlers.rs:9:21\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tracing::{info, warn, error, instrument};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `std::collections::HashMap`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/models/tenant.rs","byte_start":105,"byte_end":130,"line_start":5,"line_end":5,"column_start":5,"column_end":30,"is_primary":true,"text":[{"text":"use std::collections::HashMap;","highlight_start":5,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/models/tenant.rs","byte_start":101,"byte_end":132,"line_start":5,"line_end":6,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::collections::HashMap;","highlight_start":1,"highlight_end":31},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `std::collections::HashMap`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/models/tenant.rs:5:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m5\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::collections::HashMap;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `MfaBackupCode`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/services/mfa_service.rs","byte_start":436,"byte_end":449,"line_start":15,"line_end":15,"column_start":59,"column_end":72,"is_primary":true,"text":[{"text":"    UserMfaSecret, TrustedDevice, MfaVerificationAttempt, MfaBackupCode,","highlight_start":59,"highlight_end":72}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/services/mfa_service.rs","byte_start":434,"byte_end":449,"line_start":15,"line_end":15,"column_start":57,"column_end":72,"is_primary":true,"text":[{"text":"    UserMfaSecret, TrustedDevice, MfaVerificationAttempt, MfaBackupCode,","highlight_start":57,"highlight_end":72}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `MfaBackupCode`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/mfa_service.rs:15:59\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m15\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    UserMfaSecret, TrustedDevice, MfaVerificationAttempt, MfaBackupCode,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                           \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `ValidationError`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/services/password_reset_service.rs","byte_start":343,"byte_end":358,"line_start":13,"line_end":13,"column_start":27,"column_end":42,"is_primary":true,"text":[{"text":"use validator::{Validate, ValidationError};","highlight_start":27,"highlight_end":42}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/services/password_reset_service.rs","byte_start":341,"byte_end":358,"line_start":13,"line_end":13,"column_start":25,"column_end":42,"is_primary":true,"text":[{"text":"use validator::{Validate, ValidationError};","highlight_start":25,"highlight_end":42}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/services/password_reset_service.rs","byte_start":332,"byte_end":333,"line_start":13,"line_end":13,"column_start":16,"column_end":17,"is_primary":true,"text":[{"text":"use validator::{Validate, ValidationError};","highlight_start":16,"highlight_end":17}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/services/password_reset_service.rs","byte_start":358,"byte_end":359,"line_start":13,"line_end":13,"column_start":42,"column_end":43,"is_primary":true,"text":[{"text":"use validator::{Validate, ValidationError};","highlight_start":42,"highlight_end":43}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `ValidationError`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/password_reset_service.rs:13:27\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse validator::{Validate, ValidationError};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                           \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `HashMap`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/services/rbac_service.rs","byte_start":241,"byte_end":248,"line_start":7,"line_end":7,"column_start":24,"column_end":31,"is_primary":true,"text":[{"text":"use std::collections::{HashMap, HashSet};","highlight_start":24,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/services/rbac_service.rs","byte_start":241,"byte_end":250,"line_start":7,"line_end":7,"column_start":24,"column_end":33,"is_primary":true,"text":[{"text":"use std::collections::{HashMap, HashSet};","highlight_start":24,"highlight_end":33}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/services/rbac_service.rs","byte_start":240,"byte_end":241,"line_start":7,"line_end":7,"column_start":23,"column_end":24,"is_primary":true,"text":[{"text":"use std::collections::{HashMap, HashSet};","highlight_start":23,"highlight_end":24}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/services/rbac_service.rs","byte_start":257,"byte_end":258,"line_start":7,"line_end":7,"column_start":40,"column_end":41,"is_primary":true,"text":[{"text":"use std::collections::{HashMap, HashSet};","highlight_start":40,"highlight_end":41}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `HashMap`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/rbac_service.rs:7:24\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::collections::{HashMap, HashSet};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `warn`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/services/rbac_service.rs","byte_start":309,"byte_end":313,"line_start":9,"line_end":9,"column_start":28,"column_end":32,"is_primary":true,"text":[{"text":"use tracing::{error, info, warn};","highlight_start":28,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/services/rbac_service.rs","byte_start":307,"byte_end":313,"line_start":9,"line_end":9,"column_start":26,"column_end":32,"is_primary":true,"text":[{"text":"use tracing::{error, info, warn};","highlight_start":26,"highlight_end":32}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `warn`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/rbac_service.rs:9:28\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tracing::{error, info, warn};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `crate::utils::errors::AppError`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/services/email_verification_service.rs","byte_start":442,"byte_end":472,"line_start":15,"line_end":15,"column_start":5,"column_end":35,"is_primary":true,"text":[{"text":"use crate::utils::errors::AppError;","highlight_start":5,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/services/email_verification_service.rs","byte_start":438,"byte_end":474,"line_start":15,"line_end":16,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use crate::utils::errors::AppError;","highlight_start":1,"highlight_end":36},{"text":"use crate::services::email_service::{EmailService, EmailError};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `crate::utils::errors::AppError`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/email_verification_service.rs:15:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m15\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::utils::errors::AppError;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `EmailError`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/services/email_verification_service.rs","byte_start":525,"byte_end":535,"line_start":16,"line_end":16,"column_start":52,"column_end":62,"is_primary":true,"text":[{"text":"use crate::services::email_service::{EmailService, EmailError};","highlight_start":52,"highlight_end":62}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/services/email_verification_service.rs","byte_start":523,"byte_end":535,"line_start":16,"line_end":16,"column_start":50,"column_end":62,"is_primary":true,"text":[{"text":"use crate::services::email_service::{EmailService, EmailError};","highlight_start":50,"highlight_end":62}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/services/email_verification_service.rs","byte_start":510,"byte_end":511,"line_start":16,"line_end":16,"column_start":37,"column_end":38,"is_primary":true,"text":[{"text":"use crate::services::email_service::{EmailService, EmailError};","highlight_start":37,"highlight_end":38}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/services/email_verification_service.rs","byte_start":535,"byte_end":536,"line_start":16,"line_end":16,"column_start":62,"column_end":63,"is_primary":true,"text":[{"text":"use crate::services::email_service::{EmailService, EmailError};","highlight_start":62,"highlight_end":63}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `EmailError`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/email_verification_service.rs:16:52\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m16\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::services::email_service::{EmailService, EmailError};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                    \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `DateTime`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/services/oauth_service.rs","byte_start":82,"byte_end":90,"line_start":4,"line_end":4,"column_start":14,"column_end":22,"is_primary":true,"text":[{"text":"use chrono::{DateTime, Utc, Duration};","highlight_start":14,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/services/oauth_service.rs","byte_start":82,"byte_end":92,"line_start":4,"line_end":4,"column_start":14,"column_end":24,"is_primary":true,"text":[{"text":"use chrono::{DateTime, Utc, Duration};","highlight_start":14,"highlight_end":24}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `DateTime`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/oauth_service.rs:4:14\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse chrono::{DateTime, Utc, Duration};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `error` and `warn`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/services/oauth_service.rs","byte_start":128,"byte_end":132,"line_start":5,"line_end":5,"column_start":21,"column_end":25,"is_primary":true,"text":[{"text":"use tracing::{info, warn, error};","highlight_start":21,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/services/oauth_service.rs","byte_start":134,"byte_end":139,"line_start":5,"line_end":5,"column_start":27,"column_end":32,"is_primary":true,"text":[{"text":"use tracing::{info, warn, error};","highlight_start":27,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src/services/oauth_service.rs","byte_start":126,"byte_end":139,"line_start":5,"line_end":5,"column_start":19,"column_end":32,"is_primary":true,"text":[{"text":"use tracing::{info, warn, error};","highlight_start":19,"highlight_end":32}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/services/oauth_service.rs","byte_start":121,"byte_end":122,"line_start":5,"line_end":5,"column_start":14,"column_end":15,"is_primary":true,"text":[{"text":"use tracing::{info, warn, error};","highlight_start":14,"highlight_end":15}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/services/oauth_service.rs","byte_start":139,"byte_end":140,"line_start":5,"line_end":5,"column_start":32,"column_end":33,"is_primary":true,"text":[{"text":"use tracing::{info, warn, error};","highlight_start":32,"highlight_end":33}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `error` and `warn`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/oauth_service.rs:5:21\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m5\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tracing::{info, warn, error};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `Config`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/services/oauth_service.rs","byte_start":208,"byte_end":214,"line_start":9,"line_end":9,"column_start":21,"column_end":27,"is_primary":true,"text":[{"text":"use crate::config::{Config, OAuthConfig};","highlight_start":21,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/services/oauth_service.rs","byte_start":208,"byte_end":216,"line_start":9,"line_end":9,"column_start":21,"column_end":29,"is_primary":true,"text":[{"text":"use crate::config::{Config, OAuthConfig};","highlight_start":21,"highlight_end":29}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/services/oauth_service.rs","byte_start":207,"byte_end":208,"line_start":9,"line_end":9,"column_start":20,"column_end":21,"is_primary":true,"text":[{"text":"use crate::config::{Config, OAuthConfig};","highlight_start":20,"highlight_end":21}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/services/oauth_service.rs","byte_start":227,"byte_end":228,"line_start":9,"line_end":9,"column_start":40,"column_end":41,"is_primary":true,"text":[{"text":"use crate::config::{Config, OAuthConfig};","highlight_start":40,"highlight_end":41}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `Config`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/oauth_service.rs:9:21\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::config::{Config, OAuthConfig};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `RedirectUrl`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/services/oauth_service.rs","byte_start":17257,"byte_end":17268,"line_start":477,"line_end":477,"column_start":5,"column_end":16,"is_primary":true,"text":[{"text":"    RedirectUrl, Scope, TokenUrl, AuthorizationCode, TokenResponse,","highlight_start":5,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/services/oauth_service.rs","byte_start":17251,"byte_end":17268,"line_start":476,"line_end":477,"column_start":84,"column_end":16,"is_primary":true,"text":[{"text":"    AuthUrl, ClientId, ClientSecret, CsrfToken, PkceCodeChallenge, PkceCodeVerifier,","highlight_start":84,"highlight_end":85},{"text":"    RedirectUrl, Scope, TokenUrl, AuthorizationCode, TokenResponse,","highlight_start":1,"highlight_end":16}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `RedirectUrl`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/oauth_service.rs:477:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m477\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    RedirectUrl, Scope, TokenUrl, AuthorizationCode, TokenResponse,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `TenantSettings`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/services/tenant_service.rs","byte_start":168,"byte_end":182,"line_start":5,"line_end":5,"column_start":44,"column_end":58,"is_primary":true,"text":[{"text":"    TenantListRequest, TenantListResponse, TenantSettings, TenantStats","highlight_start":44,"highlight_end":58}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/services/tenant_service.rs","byte_start":166,"byte_end":182,"line_start":5,"line_end":5,"column_start":42,"column_end":58,"is_primary":true,"text":[{"text":"    TenantListRequest, TenantListResponse, TenantSettings, TenantStats","highlight_start":42,"highlight_end":58}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `TenantSettings`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/tenant_service.rs:5:44\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m5\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    TenantListRequest, TenantListResponse, TenantSettings, TenantStats\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `error` and `warn`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/services/tenant_service.rs","byte_start":255,"byte_end":259,"line_start":8,"line_end":8,"column_start":21,"column_end":25,"is_primary":true,"text":[{"text":"use tracing::{info, warn, error, instrument};","highlight_start":21,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/services/tenant_service.rs","byte_start":261,"byte_end":266,"line_start":8,"line_end":8,"column_start":27,"column_end":32,"is_primary":true,"text":[{"text":"use tracing::{info, warn, error, instrument};","highlight_start":27,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src/services/tenant_service.rs","byte_start":253,"byte_end":266,"line_start":8,"line_end":8,"column_start":19,"column_end":32,"is_primary":true,"text":[{"text":"use tracing::{info, warn, error, instrument};","highlight_start":19,"highlight_end":32}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `error` and `warn`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/tenant_service.rs:8:21\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tracing::{info, warn, error, instrument};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `ClientFeatures`, `ClientSecuritySettings`, and `RateLimits`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/services/service_client_service.rs","byte_start":369,"byte_end":383,"line_start":8,"line_end":8,"column_start":59,"column_end":73,"is_primary":true,"text":[{"text":"    ClientListRequest, ClientListResponse, ClientContext, ClientFeatures,","highlight_start":59,"highlight_end":73}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/services/service_client_service.rs","byte_start":389,"byte_end":411,"line_start":9,"line_end":9,"column_start":5,"column_end":27,"is_primary":true,"text":[{"text":"    ClientSecuritySettings, RateLimits","highlight_start":5,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/services/service_client_service.rs","byte_start":413,"byte_end":423,"line_start":9,"line_end":9,"column_start":29,"column_end":39,"is_primary":true,"text":[{"text":"    ClientSecuritySettings, RateLimits","highlight_start":29,"highlight_end":39}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src/services/service_client_service.rs","byte_start":367,"byte_end":423,"line_start":8,"line_end":9,"column_start":57,"column_end":39,"is_primary":true,"text":[{"text":"    ClientListRequest, ClientListResponse, ClientContext, ClientFeatures,","highlight_start":57,"highlight_end":74},{"text":"    ClientSecuritySettings, RateLimits","highlight_start":1,"highlight_end":39}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `ClientFeatures`, `ClientSecuritySettings`, and `RateLimits`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/service_client_service.rs:8:59\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    ClientListRequest, ClientListResponse, ClientContext, ClientFeatures,\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                           \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    ClientSecuritySettings, RateLimits\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `error` and `warn`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/services/service_client_service.rs","byte_start":532,"byte_end":536,"line_start":13,"line_end":13,"column_start":21,"column_end":25,"is_primary":true,"text":[{"text":"use tracing::{info, warn, error, instrument};","highlight_start":21,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/services/service_client_service.rs","byte_start":538,"byte_end":543,"line_start":13,"line_end":13,"column_start":27,"column_end":32,"is_primary":true,"text":[{"text":"use tracing::{info, warn, error, instrument};","highlight_start":27,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src/services/service_client_service.rs","byte_start":530,"byte_end":543,"line_start":13,"line_end":13,"column_start":19,"column_end":32,"is_primary":true,"text":[{"text":"use tracing::{info, warn, error, instrument};","highlight_start":19,"highlight_end":32}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `error` and `warn`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/service_client_service.rs:13:21\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tracing::{info, warn, error, instrument};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"src/services/mod.rs","byte_start":443,"byte_end":461,"line_start":19,"line_end":19,"column_start":9,"column_end":27,"is_primary":true,"text":[{"text":"pub use lockout_service::*;","highlight_start":9,"highlight_end":27}],"label":"the name `RateLimitInfo` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/services/mod.rs","byte_start":471,"byte_end":496,"line_start":20,"line_end":20,"column_start":9,"column_end":34,"is_primary":false,"text":[{"text":"pub use password_reset_service::*;","highlight_start":9,"highlight_end":34}],"label":"but the name `RateLimitInfo` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(ambiguous_glob_reexports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/mod.rs:19:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m19\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use lockout_service::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mthe name `RateLimitInfo` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m20\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use password_reset_service::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mbut the name `RateLimitInfo` in the type namespace is also re-exported here\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(ambiguous_glob_reexports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `HttpMessage` and `HttpResponse`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/middleware/security_headers.rs","byte_start":107,"byte_end":118,"line_start":3,"line_end":3,"column_start":12,"column_end":23,"is_primary":true,"text":[{"text":"    Error, HttpMessage, HttpResponse,","highlight_start":12,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/middleware/security_headers.rs","byte_start":120,"byte_end":132,"line_start":3,"line_end":3,"column_start":25,"column_end":37,"is_primary":true,"text":[{"text":"    Error, HttpMessage, HttpResponse,","highlight_start":25,"highlight_end":37}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src/middleware/security_headers.rs","byte_start":105,"byte_end":132,"line_start":3,"line_end":3,"column_start":10,"column_end":37,"is_primary":true,"text":[{"text":"    Error, HttpMessage, HttpResponse,","highlight_start":10,"highlight_end":37}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `HttpMessage` and `HttpResponse`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/middleware/security_headers.rs:3:12\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    Error, HttpMessage, HttpResponse,\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"use of deprecated function `rand::thread_rng`: Renamed to `rng`","code":{"code":"deprecated","explanation":null},"level":"warning","spans":[{"file_name":"src/models/oauth.rs","byte_start":4964,"byte_end":4974,"line_start":194,"line_end":194,"column_start":32,"column_end":42,"is_primary":true,"text":[{"text":"    rand::Rng::fill(&mut rand::thread_rng(), &mut bytes);","highlight_start":32,"highlight_end":42}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(deprecated)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: use of deprecated function `rand::thread_rng`: Renamed to `rng`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/models/oauth.rs:194:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m194\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    rand::Rng::fill(&mut rand::thread_rng(), &mut bytes);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(deprecated)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"use of deprecated function `rand::thread_rng`: Renamed to `rng`","code":{"code":"deprecated","explanation":null},"level":"warning","spans":[{"file_name":"src/models/oauth.rs","byte_start":5428,"byte_end":5438,"line_start":209,"line_end":209,"column_start":32,"column_end":42,"is_primary":true,"text":[{"text":"    rand::Rng::fill(&mut rand::thread_rng(), &mut bytes);","highlight_start":32,"highlight_end":42}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: use of deprecated function `rand::thread_rng`: Renamed to `rng`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/models/oauth.rs:209:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m209\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    rand::Rng::fill(&mut rand::thread_rng(), &mut bytes);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"use of deprecated function `rand::thread_rng`: Renamed to `rng`","code":{"code":"deprecated","explanation":null},"level":"warning","spans":[{"file_name":"src/services/password_service.rs","byte_start":8430,"byte_end":8440,"line_start":266,"line_end":266,"column_start":29,"column_end":39,"is_primary":true,"text":[{"text":"        let mut rng = rand::thread_rng();","highlight_start":29,"highlight_end":39}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: use of deprecated function `rand::thread_rng`: Renamed to `rng`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/password_service.rs:266:29\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m266\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut rng = rand::thread_rng();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"use of deprecated function `rand::thread_rng`: Renamed to `rng`","code":{"code":"deprecated","explanation":null},"level":"warning","spans":[{"file_name":"src/services/mfa_service.rs","byte_start":215,"byte_end":225,"line_start":10,"line_end":10,"column_start":12,"column_end":22,"is_primary":true,"text":[{"text":"use rand::{thread_rng, Rng};","highlight_start":12,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: use of deprecated function `rand::thread_rng`: Renamed to `rng`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/mfa_service.rs:10:12\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse rand::{thread_rng, Rng};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"use of deprecated function `rand::thread_rng`: Renamed to `rng`","code":{"code":"deprecated","explanation":null},"level":"warning","spans":[{"file_name":"src/services/mfa_service.rs","byte_start":2084,"byte_end":2094,"line_start":72,"line_end":72,"column_start":29,"column_end":39,"is_primary":true,"text":[{"text":"        let mut rng = rand::thread_rng();","highlight_start":29,"highlight_end":39}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: use of deprecated function `rand::thread_rng`: Renamed to `rng`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/mfa_service.rs:72:29\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m72\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut rng = rand::thread_rng();\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"use of deprecated function `rand::thread_rng`: Renamed to `rng`","code":{"code":"deprecated","explanation":null},"level":"warning","spans":[{"file_name":"src/services/mfa_service.rs","byte_start":10798,"byte_end":10808,"line_start":345,"line_end":345,"column_start":23,"column_end":33,"is_primary":true,"text":[{"text":"        let mut rng = thread_rng();","highlight_start":23,"highlight_end":33}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: use of deprecated function `rand::thread_rng`: Renamed to `rng`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/mfa_service.rs:345:23\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m345\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut rng = thread_rng();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                       \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"use of deprecated function `rand::thread_rng`: Renamed to `rng`","code":{"code":"deprecated","explanation":null},"level":"warning","spans":[{"file_name":"src/services/session_service.rs","byte_start":14997,"byte_end":15007,"line_start":465,"line_end":465,"column_start":29,"column_end":39,"is_primary":true,"text":[{"text":"        let mut rng = rand::thread_rng();","highlight_start":29,"highlight_end":39}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: use of deprecated function `rand::thread_rng`: Renamed to `rng`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/session_service.rs:465:29\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m465\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut rng = rand::thread_rng();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"use of deprecated function `rand::thread_rng`: Renamed to `rng`","code":{"code":"deprecated","explanation":null},"level":"warning","spans":[{"file_name":"src/services/session_service.rs","byte_start":15406,"byte_end":15416,"line_start":477,"line_end":477,"column_start":29,"column_end":39,"is_primary":true,"text":[{"text":"        let mut rng = rand::thread_rng();","highlight_start":29,"highlight_end":39}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: use of deprecated function `rand::thread_rng`: Renamed to `rng`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/session_service.rs:477:29\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m477\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut rng = rand::thread_rng();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"use of deprecated function `rand::thread_rng`: Renamed to `rng`","code":{"code":"deprecated","explanation":null},"level":"warning","spans":[{"file_name":"src/services/email_verification_service.rs","byte_start":150,"byte_end":160,"line_start":7,"line_end":7,"column_start":12,"column_end":22,"is_primary":true,"text":[{"text":"use rand::{thread_rng, Rng};","highlight_start":12,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: use of deprecated function `rand::thread_rng`: Renamed to `rng`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/email_verification_service.rs:7:12\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse rand::{thread_rng, Rng};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"use of deprecated function `rand::thread_rng`: Renamed to `rng`","code":{"code":"deprecated","explanation":null},"level":"warning","spans":[{"file_name":"src/services/email_verification_service.rs","byte_start":7369,"byte_end":7379,"line_start":213,"line_end":213,"column_start":23,"column_end":33,"is_primary":true,"text":[{"text":"        let mut rng = thread_rng();","highlight_start":23,"highlight_end":33}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: use of deprecated function `rand::thread_rng`: Renamed to `rng`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/email_verification_service.rs:213:23\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m213\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut rng = thread_rng();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                       \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"use of deprecated function `rand::thread_rng`: Renamed to `rng`","code":{"code":"deprecated","explanation":null},"level":"warning","spans":[{"file_name":"src/services/oauth_service.rs","byte_start":17030,"byte_end":17040,"line_start":470,"line_end":470,"column_start":32,"column_end":42,"is_primary":true,"text":[{"text":"    rand::Rng::fill(&mut rand::thread_rng(), &mut bytes);","highlight_start":32,"highlight_end":42}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: use of deprecated function `rand::thread_rng`: Renamed to `rng`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/oauth_service.rs:470:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m470\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    rand::Rng::fill(&mut rand::thread_rng(), &mut bytes);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"use of deprecated function `rand::thread_rng`: Renamed to `rng`","code":{"code":"deprecated","explanation":null},"level":"warning","spans":[{"file_name":"src/utils/crypto.rs","byte_start":16,"byte_end":26,"line_start":1,"line_end":1,"column_start":17,"column_end":27,"is_primary":true,"text":[{"text":"use rand::{Rng, thread_rng};","highlight_start":17,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: use of deprecated function `rand::thread_rng`: Renamed to `rng`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/utils/crypto.rs:1:17\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse rand::{Rng, thread_rng};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"use of deprecated function `rand::thread_rng`: Renamed to `rng`","code":{"code":"deprecated","explanation":null},"level":"warning","spans":[{"file_name":"src/utils/crypto.rs","byte_start":184,"byte_end":194,"line_start":6,"line_end":6,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    thread_rng()","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: use of deprecated function `rand::thread_rng`: Renamed to `rng`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/utils/crypto.rs:6:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    thread_rng()\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"use of deprecated function `rand::thread_rng`: Renamed to `rng`","code":{"code":"deprecated","explanation":null},"level":"warning","spans":[{"file_name":"src/utils/crypto.rs","byte_start":466,"byte_end":476,"line_start":15,"line_end":15,"column_start":19,"column_end":29,"is_primary":true,"text":[{"text":"    let mut rng = thread_rng();","highlight_start":19,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: use of deprecated function `rand::thread_rng`: Renamed to `rng`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/utils/crypto.rs:15:19\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m15\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut rng = thread_rng();\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                   \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"use of deprecated method `rand::Rng::r#gen`: Renamed to `random` to avoid conflict with the new `gen` keyword in Rust 2024.","code":{"code":"deprecated","explanation":null},"level":"warning","spans":[{"file_name":"src/services/mfa_service.rs","byte_start":2154,"byte_end":2157,"line_start":73,"line_end":73,"column_start":57,"column_end":60,"is_primary":true,"text":[{"text":"        let secret_bytes: Vec<u8> = (0..20).map(|_| rng.gen()).collect();","highlight_start":57,"highlight_end":60}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: use of deprecated method `rand::Rng::r#gen`: Renamed to `random` to avoid conflict with the new `gen` keyword in Rust 2024.\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/mfa_service.rs:73:57\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m73\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let secret_bytes: Vec<u8> = (0..20).map(|_| rng.gen()).collect();\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `success` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"src/services/mfa_service.rs","byte_start":4790,"byte_end":4797,"line_start":159,"line_end":159,"column_start":17,"column_end":24,"is_primary":true,"text":[{"text":"        let mut success = false;","highlight_start":17,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"`#[warn(unused_assignments)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: value assigned to `success` is never read\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/mfa_service.rs:159:17\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m159\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut success = false;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_assignments)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `oauth_account`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/services/oauth_service.rs","byte_start":5747,"byte_end":5760,"line_start":155,"line_end":155,"column_start":13,"column_end":26,"is_primary":true,"text":[{"text":"        let oauth_account = self.create_oauth_account(","highlight_start":13,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/services/oauth_service.rs","byte_start":5747,"byte_end":5760,"line_start":155,"line_end":155,"column_start":13,"column_end":26,"is_primary":true,"text":[{"text":"        let oauth_account = self.create_oauth_account(","highlight_start":13,"highlight_end":26}],"label":null,"suggested_replacement":"_oauth_account","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `oauth_account`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/oauth_service.rs:155:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m155\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let oauth_account = self.create_oauth_account(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_oauth_account`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"this function takes 1 argument but 4 arguments were supplied","code":{"code":"E0061","explanation":"An invalid number of arguments was passed when calling a function.\n\nErroneous code example:\n\n```compile_fail,E0061\nfn f(u: i32) {}\n\nf(); // error!\n```\n\nThe number of arguments passed to a function must match the number of arguments\nspecified in the function signature.\n\nFor example, a function like:\n\n```\nfn f(a: u16, b: &str) {}\n```\n\nMust always be called with exactly two arguments, e.g., `f(2, \"test\")`.\n\nNote that Rust does not have a notion of optional function arguments or\nvariadic functions (except for its C-FFI).\n"},"level":"error","spans":[{"file_name":"src/services/oauth_service.rs","byte_start":18863,"byte_end":18928,"line_start":511,"line_end":511,"column_start":13,"column_end":78,"is_primary":false,"text":[{"text":"            Some(ClientSecret::new(self.config.google.client_secret.clone())),","highlight_start":13,"highlight_end":78}],"label":"unexpected argument #2 of type `std::option::Option<ClientSecret>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/services/oauth_service.rs","byte_start":18942,"byte_end":19023,"line_start":512,"line_end":512,"column_start":13,"column_end":94,"is_primary":false,"text":[{"text":"            AuthUrl::new(\"https://accounts.google.com/o/oauth2/v2/auth\".to_string()).unwrap(),","highlight_start":13,"highlight_end":94}],"label":"unexpected argument #3 of type `AuthUrl`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/services/oauth_service.rs","byte_start":19037,"byte_end":19123,"line_start":513,"line_end":513,"column_start":13,"column_end":99,"is_primary":false,"text":[{"text":"            Some(TokenUrl::new(\"https://www.googleapis.com/oauth2/v4/token\".to_string()).unwrap()),","highlight_start":13,"highlight_end":99}],"label":"unexpected argument #4 of type `std::option::Option<TokenUrl>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/services/oauth_service.rs","byte_start":18768,"byte_end":18784,"line_start":509,"line_end":509,"column_start":22,"column_end":38,"is_primary":true,"text":[{"text":"        let client = BasicClient::new(","highlight_start":22,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"associated function defined here","code":null,"level":"note","spans":[{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/oauth2-5.0.0/src/client.rs","byte_start":7611,"byte_end":7614,"line_start":190,"line_end":190,"column_start":12,"column_end":15,"is_primary":true,"text":[{"text":"    pub fn new(client_id: ClientId) -> Self {","highlight_start":12,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"remove the extra arguments","code":null,"level":"help","spans":[{"file_name":"src/services/oauth_service.rs","byte_start":18849,"byte_end":18928,"line_start":510,"line_end":511,"column_start":64,"column_end":78,"is_primary":true,"text":[{"text":"            ClientId::new(self.config.google.client_id.clone()),","highlight_start":64,"highlight_end":65},{"text":"            Some(ClientSecret::new(self.config.google.client_secret.clone())),","highlight_start":1,"highlight_end":78}],"label":null,"suggested_replacement":"","suggestion_applicability":"HasPlaceholders","expansion":null},{"file_name":"src/services/oauth_service.rs","byte_start":18928,"byte_end":19023,"line_start":511,"line_end":512,"column_start":78,"column_end":94,"is_primary":true,"text":[{"text":"            Some(ClientSecret::new(self.config.google.client_secret.clone())),","highlight_start":78,"highlight_end":79},{"text":"            AuthUrl::new(\"https://accounts.google.com/o/oauth2/v2/auth\".to_string()).unwrap(),","highlight_start":1,"highlight_end":94}],"label":null,"suggested_replacement":"","suggestion_applicability":"HasPlaceholders","expansion":null},{"file_name":"src/services/oauth_service.rs","byte_start":19023,"byte_end":19123,"line_start":512,"line_end":513,"column_start":94,"column_end":99,"is_primary":true,"text":[{"text":"            AuthUrl::new(\"https://accounts.google.com/o/oauth2/v2/auth\".to_string()).unwrap(),","highlight_start":94,"highlight_end":95},{"text":"            Some(TokenUrl::new(\"https://www.googleapis.com/oauth2/v4/token\".to_string()).unwrap()),","highlight_start":1,"highlight_end":99}],"label":null,"suggested_replacement":"","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0061]\u001b[0m\u001b[0m\u001b[1m: this function takes 1 argument but 4 arguments were supplied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/oauth_service.rs:509:22\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m509\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let client = BasicClient::new(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m510\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            ClientId::new(self.config.google.client_id.clone()),\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m511\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            Some(ClientSecret::new(self.config.google.client_secret.clone())),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----------------------------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12munexpected argument #2 of type `std::option::Option<ClientSecret>`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m512\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            AuthUrl::new(\"https://accounts.google.com/o/oauth2/v2/auth\".to_string()).unwrap(),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m---------------------------------------------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12munexpected argument #3 of type `AuthUrl`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m513\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            Some(TokenUrl::new(\"https://www.googleapis.com/oauth2/v4/token\".to_string()).unwrap()),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------------------------------------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12munexpected argument #4 of type `std::option::Option<TokenUrl>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: associated function defined here\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/oauth2-5.0.0/src/client.rs:190:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m190\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new(client_id: ClientId) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: remove the extra arguments\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m510\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m            ClientId::new(self.config.google.client_id.clone())\u001b[0m\u001b[0m\u001b[38;5;9m,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m511\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m-             Some(ClientSecret::new(self.config.google.client_secret.clone())),\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m510\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m            ClientId::new(self.config.google.client_id.clone()),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `exchange_code` found for struct `Client<StandardErrorResponse<BasicErrorResponseType>, StandardTokenResponse<EmptyExtraTokenFields, ...>, ..., ..., ...>` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src/services/oauth_service.rs","byte_start":19176,"byte_end":19189,"line_start":516,"line_end":516,"column_start":40,"column_end":53,"is_primary":true,"text":[{"text":"        let mut token_request = client.exchange_code(AuthorizationCode::new(code.to_string()));","highlight_start":40,"highlight_end":53}],"label":"method not found in `Client<StandardErrorResponse<BasicErrorResponseType>, StandardTokenResponse<EmptyExtraTokenFields, ...>, ..., ..., ...>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"the method was found for\n- `oauth2::Client<TE, TR, TIR, RT, TRE, HasAuthUrl, HasDeviceAuthUrl, HasIntrospectionUrl, HasRevocationUrl, EndpointMaybeSet>`\n- `oauth2::Client<TE, TR, TIR, RT, TRE, HasAuthUrl, HasDeviceAuthUrl, HasIntrospectionUrl, HasRevocationUrl, EndpointSet>`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m: no method named `exchange_code` found for struct `Client<StandardErrorResponse<BasicErrorResponseType>, StandardTokenResponse<EmptyExtraTokenFields, ...>, ..., ..., ...>` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/oauth_service.rs:516:40\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m516\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut token_request = client.exchange_code(AuthorizationCode::new(code.to_string()));\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmethod not found in `Client<StandardErrorResponse<BasicErrorResponseType>, StandardTokenResponse<EmptyExtraTokenFields, ...>, ..., ..., ...>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: the method was found for\u001b[0m\n\u001b[0m            - `oauth2::Client<TE, TR, TIR, RT, TRE, HasAuthUrl, HasDeviceAuthUrl, HasIntrospectionUrl, HasRevocationUrl, EndpointMaybeSet>`\u001b[0m\n\u001b[0m            - `oauth2::Client<TE, TR, TIR, RT, TRE, HasAuthUrl, HasDeviceAuthUrl, HasIntrospectionUrl, HasRevocationUrl, EndpointSet>`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"this function takes 1 argument but 4 arguments were supplied","code":{"code":"E0061","explanation":"An invalid number of arguments was passed when calling a function.\n\nErroneous code example:\n\n```compile_fail,E0061\nfn f(u: i32) {}\n\nf(); // error!\n```\n\nThe number of arguments passed to a function must match the number of arguments\nspecified in the function signature.\n\nFor example, a function like:\n\n```\nfn f(a: u16, b: &str) {}\n```\n\nMust always be called with exactly two arguments, e.g., `f(2, \"test\")`.\n\nNote that Rust does not have a notion of optional function arguments or\nvariadic functions (except for its C-FFI).\n"},"level":"error","spans":[{"file_name":"src/services/oauth_service.rs","byte_start":22009,"byte_end":22074,"line_start":581,"line_end":581,"column_start":13,"column_end":78,"is_primary":false,"text":[{"text":"            Some(ClientSecret::new(self.config.github.client_secret.clone())),","highlight_start":13,"highlight_end":78}],"label":"unexpected argument #2 of type `std::option::Option<ClientSecret>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/services/oauth_service.rs","byte_start":22088,"byte_end":22165,"line_start":582,"line_end":582,"column_start":13,"column_end":90,"is_primary":false,"text":[{"text":"            AuthUrl::new(\"https://github.com/login/oauth/authorize\".to_string()).unwrap(),","highlight_start":13,"highlight_end":90}],"label":"unexpected argument #3 of type `AuthUrl`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/services/oauth_service.rs","byte_start":22179,"byte_end":22266,"line_start":583,"line_end":583,"column_start":13,"column_end":100,"is_primary":false,"text":[{"text":"            Some(TokenUrl::new(\"https://github.com/login/oauth/access_token\".to_string()).unwrap()),","highlight_start":13,"highlight_end":100}],"label":"unexpected argument #4 of type `std::option::Option<TokenUrl>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/services/oauth_service.rs","byte_start":21914,"byte_end":21930,"line_start":579,"line_end":579,"column_start":22,"column_end":38,"is_primary":true,"text":[{"text":"        let client = BasicClient::new(","highlight_start":22,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"associated function defined here","code":null,"level":"note","spans":[{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/oauth2-5.0.0/src/client.rs","byte_start":7611,"byte_end":7614,"line_start":190,"line_end":190,"column_start":12,"column_end":15,"is_primary":true,"text":[{"text":"    pub fn new(client_id: ClientId) -> Self {","highlight_start":12,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"remove the extra arguments","code":null,"level":"help","spans":[{"file_name":"src/services/oauth_service.rs","byte_start":21995,"byte_end":22074,"line_start":580,"line_end":581,"column_start":64,"column_end":78,"is_primary":true,"text":[{"text":"            ClientId::new(self.config.github.client_id.clone()),","highlight_start":64,"highlight_end":65},{"text":"            Some(ClientSecret::new(self.config.github.client_secret.clone())),","highlight_start":1,"highlight_end":78}],"label":null,"suggested_replacement":"","suggestion_applicability":"HasPlaceholders","expansion":null},{"file_name":"src/services/oauth_service.rs","byte_start":22074,"byte_end":22165,"line_start":581,"line_end":582,"column_start":78,"column_end":90,"is_primary":true,"text":[{"text":"            Some(ClientSecret::new(self.config.github.client_secret.clone())),","highlight_start":78,"highlight_end":79},{"text":"            AuthUrl::new(\"https://github.com/login/oauth/authorize\".to_string()).unwrap(),","highlight_start":1,"highlight_end":90}],"label":null,"suggested_replacement":"","suggestion_applicability":"HasPlaceholders","expansion":null},{"file_name":"src/services/oauth_service.rs","byte_start":22165,"byte_end":22266,"line_start":582,"line_end":583,"column_start":90,"column_end":100,"is_primary":true,"text":[{"text":"            AuthUrl::new(\"https://github.com/login/oauth/authorize\".to_string()).unwrap(),","highlight_start":90,"highlight_end":91},{"text":"            Some(TokenUrl::new(\"https://github.com/login/oauth/access_token\".to_string()).unwrap()),","highlight_start":1,"highlight_end":100}],"label":null,"suggested_replacement":"","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0061]\u001b[0m\u001b[0m\u001b[1m: this function takes 1 argument but 4 arguments were supplied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/oauth_service.rs:579:22\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m579\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let client = BasicClient::new(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m580\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            ClientId::new(self.config.github.client_id.clone()),\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m581\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            Some(ClientSecret::new(self.config.github.client_secret.clone())),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----------------------------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12munexpected argument #2 of type `std::option::Option<ClientSecret>`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m582\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            AuthUrl::new(\"https://github.com/login/oauth/authorize\".to_string()).unwrap(),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----------------------------------------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12munexpected argument #3 of type `AuthUrl`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m583\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            Some(TokenUrl::new(\"https://github.com/login/oauth/access_token\".to_string()).unwrap()),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m---------------------------------------------------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12munexpected argument #4 of type `std::option::Option<TokenUrl>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: associated function defined here\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/oauth2-5.0.0/src/client.rs:190:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m190\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new(client_id: ClientId) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: remove the extra arguments\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m580\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m            ClientId::new(self.config.github.client_id.clone())\u001b[0m\u001b[0m\u001b[38;5;9m,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m581\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m-             Some(ClientSecret::new(self.config.github.client_secret.clone())),\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m580\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m            ClientId::new(self.config.github.client_id.clone()),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `exchange_code` found for struct `Client<StandardErrorResponse<BasicErrorResponseType>, StandardTokenResponse<EmptyExtraTokenFields, ...>, ..., ..., ...>` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src/services/oauth_service.rs","byte_start":22309,"byte_end":22329,"line_start":586,"line_end":587,"column_start":30,"column_end":14,"is_primary":false,"text":[{"text":"        let token_response = client","highlight_start":30,"highlight_end":36},{"text":"            .exchange_code(AuthorizationCode::new(code.to_string()))","highlight_start":1,"highlight_end":14}],"label":"","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/services/oauth_service.rs","byte_start":22329,"byte_end":22342,"line_start":587,"line_end":587,"column_start":14,"column_end":27,"is_primary":true,"text":[{"text":"            .exchange_code(AuthorizationCode::new(code.to_string()))","highlight_start":14,"highlight_end":27}],"label":"method not found in `Client<StandardErrorResponse<BasicErrorResponseType>, StandardTokenResponse<EmptyExtraTokenFields, ...>, ..., ..., ...>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"the method was found for\n- `oauth2::Client<TE, TR, TIR, RT, TRE, HasAuthUrl, HasDeviceAuthUrl, HasIntrospectionUrl, HasRevocationUrl, EndpointMaybeSet>`\n- `oauth2::Client<TE, TR, TIR, RT, TRE, HasAuthUrl, HasDeviceAuthUrl, HasIntrospectionUrl, HasRevocationUrl, EndpointSet>`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m: no method named `exchange_code` found for struct `Client<StandardErrorResponse<BasicErrorResponseType>, StandardTokenResponse<EmptyExtraTokenFields, ...>, ..., ..., ...>` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/oauth_service.rs:587:14\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m586\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m        let token_response = client\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m ______________________________-\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m587\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .exchange_code(AuthorizationCode::new(code.to_string()))\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmethod not found in `Client<StandardErrorResponse<BasicErrorResponseType>, StandardTokenResponse<EmptyExtraTokenFields, ...>, ..., ..., ...>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|_____________|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: the method was found for\u001b[0m\n\u001b[0m            - `oauth2::Client<TE, TR, TIR, RT, TRE, HasAuthUrl, HasDeviceAuthUrl, HasIntrospectionUrl, HasRevocationUrl, EndpointMaybeSet>`\u001b[0m\n\u001b[0m            - `oauth2::Client<TE, TR, TIR, RT, TRE, HasAuthUrl, HasDeviceAuthUrl, HasIntrospectionUrl, HasRevocationUrl, EndpointSet>`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"this function takes 1 argument but 4 arguments were supplied","code":{"code":"E0061","explanation":"An invalid number of arguments was passed when calling a function.\n\nErroneous code example:\n\n```compile_fail,E0061\nfn f(u: i32) {}\n\nf(); // error!\n```\n\nThe number of arguments passed to a function must match the number of arguments\nspecified in the function signature.\n\nFor example, a function like:\n\n```\nfn f(a: u16, b: &str) {}\n```\n\nMust always be called with exactly two arguments, e.g., `f(2, \"test\")`.\n\nNote that Rust does not have a notion of optional function arguments or\nvariadic functions (except for its C-FFI).\n"},"level":"error","spans":[{"file_name":"src/services/oauth_service.rs","byte_start":26181,"byte_end":26249,"line_start":675,"line_end":675,"column_start":13,"column_end":81,"is_primary":false,"text":[{"text":"            Some(ClientSecret::new(self.config.microsoft.client_secret.clone())),","highlight_start":13,"highlight_end":81}],"label":"unexpected argument #2 of type `std::option::Option<ClientSecret>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/services/oauth_service.rs","byte_start":26263,"byte_end":26362,"line_start":676,"line_end":676,"column_start":13,"column_end":112,"is_primary":false,"text":[{"text":"            AuthUrl::new(\"https://login.microsoftonline.com/common/oauth2/v2.0/authorize\".to_string()).unwrap(),","highlight_start":13,"highlight_end":112}],"label":"unexpected argument #3 of type `AuthUrl`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/services/oauth_service.rs","byte_start":26376,"byte_end":26478,"line_start":677,"line_end":677,"column_start":13,"column_end":115,"is_primary":false,"text":[{"text":"            Some(TokenUrl::new(\"https://login.microsoftonline.com/common/oauth2/v2.0/token\".to_string()).unwrap()),","highlight_start":13,"highlight_end":115}],"label":"unexpected argument #4 of type `std::option::Option<TokenUrl>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/services/oauth_service.rs","byte_start":26083,"byte_end":26099,"line_start":673,"line_end":673,"column_start":22,"column_end":38,"is_primary":true,"text":[{"text":"        let client = BasicClient::new(","highlight_start":22,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"associated function defined here","code":null,"level":"note","spans":[{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/oauth2-5.0.0/src/client.rs","byte_start":7611,"byte_end":7614,"line_start":190,"line_end":190,"column_start":12,"column_end":15,"is_primary":true,"text":[{"text":"    pub fn new(client_id: ClientId) -> Self {","highlight_start":12,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"remove the extra arguments","code":null,"level":"help","spans":[{"file_name":"src/services/oauth_service.rs","byte_start":26167,"byte_end":26249,"line_start":674,"line_end":675,"column_start":67,"column_end":81,"is_primary":true,"text":[{"text":"            ClientId::new(self.config.microsoft.client_id.clone()),","highlight_start":67,"highlight_end":68},{"text":"            Some(ClientSecret::new(self.config.microsoft.client_secret.clone())),","highlight_start":1,"highlight_end":81}],"label":null,"suggested_replacement":"","suggestion_applicability":"HasPlaceholders","expansion":null},{"file_name":"src/services/oauth_service.rs","byte_start":26249,"byte_end":26362,"line_start":675,"line_end":676,"column_start":81,"column_end":112,"is_primary":true,"text":[{"text":"            Some(ClientSecret::new(self.config.microsoft.client_secret.clone())),","highlight_start":81,"highlight_end":82},{"text":"            AuthUrl::new(\"https://login.microsoftonline.com/common/oauth2/v2.0/authorize\".to_string()).unwrap(),","highlight_start":1,"highlight_end":112}],"label":null,"suggested_replacement":"","suggestion_applicability":"HasPlaceholders","expansion":null},{"file_name":"src/services/oauth_service.rs","byte_start":26362,"byte_end":26478,"line_start":676,"line_end":677,"column_start":112,"column_end":115,"is_primary":true,"text":[{"text":"            AuthUrl::new(\"https://login.microsoftonline.com/common/oauth2/v2.0/authorize\".to_string()).unwrap(),","highlight_start":112,"highlight_end":113},{"text":"            Some(TokenUrl::new(\"https://login.microsoftonline.com/common/oauth2/v2.0/token\".to_string()).unwrap()),","highlight_start":1,"highlight_end":115}],"label":null,"suggested_replacement":"","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0061]\u001b[0m\u001b[0m\u001b[1m: this function takes 1 argument but 4 arguments were supplied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/oauth_service.rs:673:22\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m673\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let client = BasicClient::new(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m674\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            ClientId::new(self.config.microsoft.client_id.clone()),\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m675\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            Some(ClientSecret::new(self.config.microsoft.client_secret.clone())),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------------------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12munexpected argument #2 of type `std::option::Option<ClientSecret>`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m676\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            AuthUrl::new(\"https://login.microsoftonline.com/common/oauth2/v2.0/authorize\".to_string()).unwrap(),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m---------------------------------------------------------------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12munexpected argument #3 of type `AuthUrl`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m677\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            Some(TokenUrl::new(\"https://login.microsoftonline.com/common/oauth2/v2.0/token\".to_string()).unwrap()),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m------------------------------------------------------------------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12munexpected argument #4 of type `std::option::Option<TokenUrl>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: associated function defined here\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/oauth2-5.0.0/src/client.rs:190:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m190\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new(client_id: ClientId) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: remove the extra arguments\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m674\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m            ClientId::new(self.config.microsoft.client_id.clone())\u001b[0m\u001b[0m\u001b[38;5;9m,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m675\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m-             Some(ClientSecret::new(self.config.microsoft.client_secret.clone())),\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m674\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m            ClientId::new(self.config.microsoft.client_id.clone()),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `exchange_code` found for struct `Client<StandardErrorResponse<BasicErrorResponseType>, StandardTokenResponse<EmptyExtraTokenFields, ...>, ..., ..., ...>` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src/services/oauth_service.rs","byte_start":26531,"byte_end":26544,"line_start":680,"line_end":680,"column_start":40,"column_end":53,"is_primary":true,"text":[{"text":"        let mut token_request = client.exchange_code(AuthorizationCode::new(code.to_string()));","highlight_start":40,"highlight_end":53}],"label":"method not found in `Client<StandardErrorResponse<BasicErrorResponseType>, StandardTokenResponse<EmptyExtraTokenFields, ...>, ..., ..., ...>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"the method was found for\n- `oauth2::Client<TE, TR, TIR, RT, TRE, HasAuthUrl, HasDeviceAuthUrl, HasIntrospectionUrl, HasRevocationUrl, EndpointMaybeSet>`\n- `oauth2::Client<TE, TR, TIR, RT, TRE, HasAuthUrl, HasDeviceAuthUrl, HasIntrospectionUrl, HasRevocationUrl, EndpointSet>`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m: no method named `exchange_code` found for struct `Client<StandardErrorResponse<BasicErrorResponseType>, StandardTokenResponse<EmptyExtraTokenFields, ...>, ..., ..., ...>` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/oauth_service.rs:680:40\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m680\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut token_request = client.exchange_code(AuthorizationCode::new(code.to_string()));\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmethod not found in `Client<StandardErrorResponse<BasicErrorResponseType>, StandardTokenResponse<EmptyExtraTokenFields, ...>, ..., ..., ...>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: the method was found for\u001b[0m\n\u001b[0m            - `oauth2::Client<TE, TR, TIR, RT, TRE, HasAuthUrl, HasDeviceAuthUrl, HasIntrospectionUrl, HasRevocationUrl, EndpointMaybeSet>`\u001b[0m\n\u001b[0m            - `oauth2::Client<TE, TR, TIR, RT, TRE, HasAuthUrl, HasDeviceAuthUrl, HasIntrospectionUrl, HasRevocationUrl, EndpointSet>`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `resend`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/services/email_service.rs","byte_start":6944,"byte_end":6950,"line_start":230,"line_end":230,"column_start":9,"column_end":15,"is_primary":true,"text":[{"text":"        resend: &Resend,","highlight_start":9,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/services/email_service.rs","byte_start":6944,"byte_end":6950,"line_start":230,"line_end":230,"column_start":9,"column_end":15,"is_primary":true,"text":[{"text":"        resend: &Resend,","highlight_start":9,"highlight_end":15}],"label":null,"suggested_replacement":"_resend","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `resend`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/email_service.rs:230:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m230\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        resend: &Resend,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_resend`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"failed to resolve: use of unresolved module or unlinked crate `dotenv`","code":{"code":"E0433","explanation":"An undeclared crate, module, or type was used.\n\nErroneous code example:\n\n```compile_fail,E0433\nlet map = HashMap::new();\n// error: failed to resolve: use of undeclared type `HashMap`\n```\n\nPlease verify you didn't misspell the type/module's name or that you didn't\nforget to import it:\n\n```\nuse std::collections::HashMap; // HashMap has been imported.\nlet map: HashMap<u32, u32> = HashMap::new(); // So it can be used!\n```\n\nIf you've expected to use a crate name:\n\n```compile_fail\nuse ferris_wheel::BigO;\n// error: failed to resolve: use of undeclared module or unlinked crate\n```\n\nMake sure the crate has been added as a dependency in `Cargo.toml`.\n\nTo use a module from your current crate, add the `crate::` prefix to the path.\n"},"level":"error","spans":[{"file_name":"src/config/mod.rs","byte_start":1592,"byte_end":1598,"line_start":69,"line_end":69,"column_start":9,"column_end":15,"is_primary":true,"text":[{"text":"        dotenv::dotenv().ok();","highlight_start":9,"highlight_end":15}],"label":"use of unresolved module or unlinked crate `dotenv`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"there is a crate or module with a similar name","code":null,"level":"help","spans":[{"file_name":"src/config/mod.rs","byte_start":1592,"byte_end":1598,"line_start":69,"line_end":69,"column_start":9,"column_end":15,"is_primary":true,"text":[{"text":"        dotenv::dotenv().ok();","highlight_start":9,"highlight_end":15}],"label":null,"suggested_replacement":"dotenvy","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0433]\u001b[0m\u001b[0m\u001b[1m: failed to resolve: use of unresolved module or unlinked crate `dotenv`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/config/mod.rs:69:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m69\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        dotenv::dotenv().ok();\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9muse of unresolved module or unlinked crate `dotenv`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: there is a crate or module with a similar name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m69\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0m        dotenv\u001b[0m\u001b[0m\u001b[38;5;10my\u001b[0m\u001b[0m::dotenv().ok();\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[38;5;10m+\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"use of deprecated method `rand::Rng::gen_range`: Renamed to `random_range`","code":{"code":"deprecated","explanation":null},"level":"warning","spans":[{"file_name":"src/services/password_service.rs","byte_start":8526,"byte_end":8535,"line_start":270,"line_end":270,"column_start":31,"column_end":40,"is_primary":true,"text":[{"text":"                let idx = rng.gen_range(0..charset.len());","highlight_start":31,"highlight_end":40}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: use of deprecated method `rand::Rng::gen_range`: Renamed to `random_range`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/password_service.rs:270:31\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m270\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                let idx = rng.gen_range(0..charset.len());\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"use of deprecated method `rand::Rng::gen_range`: Renamed to `random_range`","code":{"code":"deprecated","explanation":null},"level":"warning","spans":[{"file_name":"src/services/mfa_service.rs","byte_start":10955,"byte_end":10964,"line_start":349,"line_end":349,"column_start":30,"column_end":39,"is_primary":true,"text":[{"text":"                .map(|_| rng.gen_range(0..10).to_string())","highlight_start":30,"highlight_end":39}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: use of deprecated method `rand::Rng::gen_range`: Renamed to `random_range`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/mfa_service.rs:349:30\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m349\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                .map(|_| rng.gen_range(0..10).to_string())\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"use of deprecated method `rand::Rng::gen_range`: Renamed to `random_range`","code":{"code":"deprecated","explanation":null},"level":"warning","spans":[{"file_name":"src/services/session_service.rs","byte_start":15191,"byte_end":15200,"line_start":469,"line_end":469,"column_start":27,"column_end":36,"is_primary":true,"text":[{"text":"                chars[rng.gen_range(0..chars.len())] as char","highlight_start":27,"highlight_end":36}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: use of deprecated method `rand::Rng::gen_range`: Renamed to `random_range`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/session_service.rs:469:27\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m469\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                chars[rng.gen_range(0..chars.len())] as char\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                           \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"use of deprecated method `rand::Rng::gen_range`: Renamed to `random_range`","code":{"code":"deprecated","explanation":null},"level":"warning","spans":[{"file_name":"src/services/session_service.rs","byte_start":15600,"byte_end":15609,"line_start":481,"line_end":481,"column_start":27,"column_end":36,"is_primary":true,"text":[{"text":"                chars[rng.gen_range(0..chars.len())] as char","highlight_start":27,"highlight_end":36}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: use of deprecated method `rand::Rng::gen_range`: Renamed to `random_range`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/session_service.rs:481:27\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m481\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                chars[rng.gen_range(0..chars.len())] as char\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                           \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"use of deprecated method `rand::Rng::r#gen`: Renamed to `random` to avoid conflict with the new `gen` keyword in Rust 2024.","code":{"code":"deprecated","explanation":null},"level":"warning","spans":[{"file_name":"src/services/email_verification_service.rs","byte_start":7417,"byte_end":7420,"line_start":214,"line_end":214,"column_start":35,"column_end":38,"is_primary":true,"text":[{"text":"        let bytes: [u8; 32] = rng.gen();","highlight_start":35,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: use of deprecated method `rand::Rng::r#gen`: Renamed to `random` to avoid conflict with the new `gen` keyword in Rust 2024.\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/email_verification_service.rs:214:35\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m214\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let bytes: [u8; 32] = rng.gen();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                   \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"this function takes 1 argument but 4 arguments were supplied","code":{"code":"E0061","explanation":"An invalid number of arguments was passed when calling a function.\n\nErroneous code example:\n\n```compile_fail,E0061\nfn f(u: i32) {}\n\nf(); // error!\n```\n\nThe number of arguments passed to a function must match the number of arguments\nspecified in the function signature.\n\nFor example, a function like:\n\n```\nfn f(a: u16, b: &str) {}\n```\n\nMust always be called with exactly two arguments, e.g., `f(2, \"test\")`.\n\nNote that Rust does not have a notion of optional function arguments or\nvariadic functions (except for its C-FFI).\n"},"level":"error","spans":[{"file_name":"src/services/oauth_service.rs","byte_start":17676,"byte_end":17741,"line_start":487,"line_end":487,"column_start":13,"column_end":78,"is_primary":false,"text":[{"text":"            Some(ClientSecret::new(self.config.google.client_secret.clone())),","highlight_start":13,"highlight_end":78}],"label":"unexpected argument #2 of type `std::option::Option<ClientSecret>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/services/oauth_service.rs","byte_start":17755,"byte_end":17836,"line_start":488,"line_end":488,"column_start":13,"column_end":94,"is_primary":false,"text":[{"text":"            AuthUrl::new(\"https://accounts.google.com/o/oauth2/v2/auth\".to_string()).unwrap(),","highlight_start":13,"highlight_end":94}],"label":"unexpected argument #3 of type `AuthUrl`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/services/oauth_service.rs","byte_start":17850,"byte_end":17936,"line_start":489,"line_end":489,"column_start":13,"column_end":99,"is_primary":false,"text":[{"text":"            Some(TokenUrl::new(\"https://www.googleapis.com/oauth2/v4/token\".to_string()).unwrap()),","highlight_start":13,"highlight_end":99}],"label":"unexpected argument #4 of type `std::option::Option<TokenUrl>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/services/oauth_service.rs","byte_start":17581,"byte_end":17597,"line_start":485,"line_end":485,"column_start":22,"column_end":38,"is_primary":true,"text":[{"text":"        let client = BasicClient::new(","highlight_start":22,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"associated function defined here","code":null,"level":"note","spans":[{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/oauth2-5.0.0/src/client.rs","byte_start":7611,"byte_end":7614,"line_start":190,"line_end":190,"column_start":12,"column_end":15,"is_primary":true,"text":[{"text":"    pub fn new(client_id: ClientId) -> Self {","highlight_start":12,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"remove the extra arguments","code":null,"level":"help","spans":[{"file_name":"src/services/oauth_service.rs","byte_start":17662,"byte_end":17741,"line_start":486,"line_end":487,"column_start":64,"column_end":78,"is_primary":true,"text":[{"text":"            ClientId::new(self.config.google.client_id.clone()),","highlight_start":64,"highlight_end":65},{"text":"            Some(ClientSecret::new(self.config.google.client_secret.clone())),","highlight_start":1,"highlight_end":78}],"label":null,"suggested_replacement":"","suggestion_applicability":"HasPlaceholders","expansion":null},{"file_name":"src/services/oauth_service.rs","byte_start":17741,"byte_end":17836,"line_start":487,"line_end":488,"column_start":78,"column_end":94,"is_primary":true,"text":[{"text":"            Some(ClientSecret::new(self.config.google.client_secret.clone())),","highlight_start":78,"highlight_end":79},{"text":"            AuthUrl::new(\"https://accounts.google.com/o/oauth2/v2/auth\".to_string()).unwrap(),","highlight_start":1,"highlight_end":94}],"label":null,"suggested_replacement":"","suggestion_applicability":"HasPlaceholders","expansion":null},{"file_name":"src/services/oauth_service.rs","byte_start":17836,"byte_end":17936,"line_start":488,"line_end":489,"column_start":94,"column_end":99,"is_primary":true,"text":[{"text":"            AuthUrl::new(\"https://accounts.google.com/o/oauth2/v2/auth\".to_string()).unwrap(),","highlight_start":94,"highlight_end":95},{"text":"            Some(TokenUrl::new(\"https://www.googleapis.com/oauth2/v4/token\".to_string()).unwrap()),","highlight_start":1,"highlight_end":99}],"label":null,"suggested_replacement":"","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0061]\u001b[0m\u001b[0m\u001b[1m: this function takes 1 argument but 4 arguments were supplied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/oauth_service.rs:485:22\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m485\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let client = BasicClient::new(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m486\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            ClientId::new(self.config.google.client_id.clone()),\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m487\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            Some(ClientSecret::new(self.config.google.client_secret.clone())),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----------------------------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12munexpected argument #2 of type `std::option::Option<ClientSecret>`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m488\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            AuthUrl::new(\"https://accounts.google.com/o/oauth2/v2/auth\".to_string()).unwrap(),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m---------------------------------------------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12munexpected argument #3 of type `AuthUrl`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m489\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            Some(TokenUrl::new(\"https://www.googleapis.com/oauth2/v4/token\".to_string()).unwrap()),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------------------------------------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12munexpected argument #4 of type `std::option::Option<TokenUrl>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: associated function defined here\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/oauth2-5.0.0/src/client.rs:190:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m190\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new(client_id: ClientId) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: remove the extra arguments\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m486\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m            ClientId::new(self.config.google.client_id.clone())\u001b[0m\u001b[0m\u001b[38;5;9m,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m487\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m-             Some(ClientSecret::new(self.config.google.client_secret.clone())),\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m486\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m            ClientId::new(self.config.google.client_id.clone()),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `authorize_url` found for struct `Client<StandardErrorResponse<BasicErrorResponseType>, StandardTokenResponse<EmptyExtraTokenFields, ...>, ..., ..., ...>` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src/services/oauth_service.rs","byte_start":17981,"byte_end":18001,"line_start":492,"line_end":493,"column_start":32,"column_end":14,"is_primary":false,"text":[{"text":"        let mut auth_request = client","highlight_start":32,"highlight_end":38},{"text":"            .authorize_url(|| CsrfToken::new(state.to_string()))","highlight_start":1,"highlight_end":14}],"label":"","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/services/oauth_service.rs","byte_start":18001,"byte_end":18014,"line_start":493,"line_end":493,"column_start":14,"column_end":27,"is_primary":true,"text":[{"text":"            .authorize_url(|| CsrfToken::new(state.to_string()))","highlight_start":14,"highlight_end":27}],"label":"method not found in `Client<StandardErrorResponse<BasicErrorResponseType>, StandardTokenResponse<EmptyExtraTokenFields, ...>, ..., ..., ...>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"the method was found for\n- `oauth2::Client<TE, TR, TIR, RT, TRE, EndpointMaybeSet, HasDeviceAuthUrl, HasIntrospectionUrl, HasRevocationUrl, HasTokenUrl>`\n- `oauth2::Client<TE, TR, TIR, RT, TRE, EndpointSet, HasDeviceAuthUrl, HasIntrospectionUrl, HasRevocationUrl, HasTokenUrl>`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m: no method named `authorize_url` found for struct `Client<StandardErrorResponse<BasicErrorResponseType>, StandardTokenResponse<EmptyExtraTokenFields, ...>, ..., ..., ...>` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/oauth_service.rs:493:14\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m492\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m        let mut auth_request = client\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m ________________________________-\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m493\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .authorize_url(|| CsrfToken::new(state.to_string()))\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmethod not found in `Client<StandardErrorResponse<BasicErrorResponseType>, StandardTokenResponse<EmptyExtraTokenFields, ...>, ..., ..., ...>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|_____________|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: the method was found for\u001b[0m\n\u001b[0m            - `oauth2::Client<TE, TR, TIR, RT, TRE, EndpointMaybeSet, HasDeviceAuthUrl, HasIntrospectionUrl, HasRevocationUrl, HasTokenUrl>`\u001b[0m\n\u001b[0m            - `oauth2::Client<TE, TR, TIR, RT, TRE, EndpointSet, HasDeviceAuthUrl, HasIntrospectionUrl, HasRevocationUrl, HasTokenUrl>`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"this function takes 1 argument but 4 arguments were supplied","code":{"code":"E0061","explanation":"An invalid number of arguments was passed when calling a function.\n\nErroneous code example:\n\n```compile_fail,E0061\nfn f(u: i32) {}\n\nf(); // error!\n```\n\nThe number of arguments passed to a function must match the number of arguments\nspecified in the function signature.\n\nFor example, a function like:\n\n```\nfn f(a: u16, b: &str) {}\n```\n\nMust always be called with exactly two arguments, e.g., `f(2, \"test\")`.\n\nNote that Rust does not have a notion of optional function arguments or\nvariadic functions (except for its C-FFI).\n"},"level":"error","spans":[{"file_name":"src/services/oauth_service.rs","byte_start":21273,"byte_end":21338,"line_start":565,"line_end":565,"column_start":13,"column_end":78,"is_primary":false,"text":[{"text":"            Some(ClientSecret::new(self.config.github.client_secret.clone())),","highlight_start":13,"highlight_end":78}],"label":"unexpected argument #2 of type `std::option::Option<ClientSecret>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/services/oauth_service.rs","byte_start":21352,"byte_end":21429,"line_start":566,"line_end":566,"column_start":13,"column_end":90,"is_primary":false,"text":[{"text":"            AuthUrl::new(\"https://github.com/login/oauth/authorize\".to_string()).unwrap(),","highlight_start":13,"highlight_end":90}],"label":"unexpected argument #3 of type `AuthUrl`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/services/oauth_service.rs","byte_start":21443,"byte_end":21530,"line_start":567,"line_end":567,"column_start":13,"column_end":100,"is_primary":false,"text":[{"text":"            Some(TokenUrl::new(\"https://github.com/login/oauth/access_token\".to_string()).unwrap()),","highlight_start":13,"highlight_end":100}],"label":"unexpected argument #4 of type `std::option::Option<TokenUrl>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/services/oauth_service.rs","byte_start":21178,"byte_end":21194,"line_start":563,"line_end":563,"column_start":22,"column_end":38,"is_primary":true,"text":[{"text":"        let client = BasicClient::new(","highlight_start":22,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"associated function defined here","code":null,"level":"note","spans":[{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/oauth2-5.0.0/src/client.rs","byte_start":7611,"byte_end":7614,"line_start":190,"line_end":190,"column_start":12,"column_end":15,"is_primary":true,"text":[{"text":"    pub fn new(client_id: ClientId) -> Self {","highlight_start":12,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"remove the extra arguments","code":null,"level":"help","spans":[{"file_name":"src/services/oauth_service.rs","byte_start":21259,"byte_end":21338,"line_start":564,"line_end":565,"column_start":64,"column_end":78,"is_primary":true,"text":[{"text":"            ClientId::new(self.config.github.client_id.clone()),","highlight_start":64,"highlight_end":65},{"text":"            Some(ClientSecret::new(self.config.github.client_secret.clone())),","highlight_start":1,"highlight_end":78}],"label":null,"suggested_replacement":"","suggestion_applicability":"HasPlaceholders","expansion":null},{"file_name":"src/services/oauth_service.rs","byte_start":21338,"byte_end":21429,"line_start":565,"line_end":566,"column_start":78,"column_end":90,"is_primary":true,"text":[{"text":"            Some(ClientSecret::new(self.config.github.client_secret.clone())),","highlight_start":78,"highlight_end":79},{"text":"            AuthUrl::new(\"https://github.com/login/oauth/authorize\".to_string()).unwrap(),","highlight_start":1,"highlight_end":90}],"label":null,"suggested_replacement":"","suggestion_applicability":"HasPlaceholders","expansion":null},{"file_name":"src/services/oauth_service.rs","byte_start":21429,"byte_end":21530,"line_start":566,"line_end":567,"column_start":90,"column_end":100,"is_primary":true,"text":[{"text":"            AuthUrl::new(\"https://github.com/login/oauth/authorize\".to_string()).unwrap(),","highlight_start":90,"highlight_end":91},{"text":"            Some(TokenUrl::new(\"https://github.com/login/oauth/access_token\".to_string()).unwrap()),","highlight_start":1,"highlight_end":100}],"label":null,"suggested_replacement":"","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0061]\u001b[0m\u001b[0m\u001b[1m: this function takes 1 argument but 4 arguments were supplied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/oauth_service.rs:563:22\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m563\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let client = BasicClient::new(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m564\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            ClientId::new(self.config.github.client_id.clone()),\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m565\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            Some(ClientSecret::new(self.config.github.client_secret.clone())),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----------------------------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12munexpected argument #2 of type `std::option::Option<ClientSecret>`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m566\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            AuthUrl::new(\"https://github.com/login/oauth/authorize\".to_string()).unwrap(),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----------------------------------------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12munexpected argument #3 of type `AuthUrl`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m567\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            Some(TokenUrl::new(\"https://github.com/login/oauth/access_token\".to_string()).unwrap()),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m---------------------------------------------------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12munexpected argument #4 of type `std::option::Option<TokenUrl>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: associated function defined here\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/oauth2-5.0.0/src/client.rs:190:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m190\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new(client_id: ClientId) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: remove the extra arguments\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m564\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m            ClientId::new(self.config.github.client_id.clone())\u001b[0m\u001b[0m\u001b[38;5;9m,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m565\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m-             Some(ClientSecret::new(self.config.github.client_secret.clone())),\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m564\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m            ClientId::new(self.config.github.client_id.clone()),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `authorize_url` found for struct `Client<StandardErrorResponse<BasicErrorResponseType>, StandardTokenResponse<EmptyExtraTokenFields, ...>, ..., ..., ...>` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src/services/oauth_service.rs","byte_start":21572,"byte_end":21592,"line_start":570,"line_end":571,"column_start":29,"column_end":14,"is_primary":false,"text":[{"text":"        let (auth_url, _) = client","highlight_start":29,"highlight_end":35},{"text":"            .authorize_url(|| CsrfToken::new(state.to_string()))","highlight_start":1,"highlight_end":14}],"label":"","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/services/oauth_service.rs","byte_start":21592,"byte_end":21605,"line_start":571,"line_end":571,"column_start":14,"column_end":27,"is_primary":true,"text":[{"text":"            .authorize_url(|| CsrfToken::new(state.to_string()))","highlight_start":14,"highlight_end":27}],"label":"method not found in `Client<StandardErrorResponse<BasicErrorResponseType>, StandardTokenResponse<EmptyExtraTokenFields, ...>, ..., ..., ...>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"the method was found for\n- `oauth2::Client<TE, TR, TIR, RT, TRE, EndpointMaybeSet, HasDeviceAuthUrl, HasIntrospectionUrl, HasRevocationUrl, HasTokenUrl>`\n- `oauth2::Client<TE, TR, TIR, RT, TRE, EndpointSet, HasDeviceAuthUrl, HasIntrospectionUrl, HasRevocationUrl, HasTokenUrl>`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m: no method named `authorize_url` found for struct `Client<StandardErrorResponse<BasicErrorResponseType>, StandardTokenResponse<EmptyExtraTokenFields, ...>, ..., ..., ...>` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/oauth_service.rs:571:14\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m570\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m        let (auth_url, _) = client\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m _____________________________-\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m571\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .authorize_url(|| CsrfToken::new(state.to_string()))\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmethod not found in `Client<StandardErrorResponse<BasicErrorResponseType>, StandardTokenResponse<EmptyExtraTokenFields, ...>, ..., ..., ...>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|_____________|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: the method was found for\u001b[0m\n\u001b[0m            - `oauth2::Client<TE, TR, TIR, RT, TRE, EndpointMaybeSet, HasDeviceAuthUrl, HasIntrospectionUrl, HasRevocationUrl, HasTokenUrl>`\u001b[0m\n\u001b[0m            - `oauth2::Client<TE, TR, TIR, RT, TRE, EndpointSet, HasDeviceAuthUrl, HasIntrospectionUrl, HasRevocationUrl, HasTokenUrl>`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"this function takes 1 argument but 4 arguments were supplied","code":{"code":"E0061","explanation":"An invalid number of arguments was passed when calling a function.\n\nErroneous code example:\n\n```compile_fail,E0061\nfn f(u: i32) {}\n\nf(); // error!\n```\n\nThe number of arguments passed to a function must match the number of arguments\nspecified in the function signature.\n\nFor example, a function like:\n\n```\nfn f(a: u16, b: &str) {}\n```\n\nMust always be called with exactly two arguments, e.g., `f(2, \"test\")`.\n\nNote that Rust does not have a notion of optional function arguments or\nvariadic functions (except for its C-FFI).\n"},"level":"error","spans":[{"file_name":"src/services/oauth_service.rs","byte_start":24951,"byte_end":25019,"line_start":651,"line_end":651,"column_start":13,"column_end":81,"is_primary":false,"text":[{"text":"            Some(ClientSecret::new(self.config.microsoft.client_secret.clone())),","highlight_start":13,"highlight_end":81}],"label":"unexpected argument #2 of type `std::option::Option<ClientSecret>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/services/oauth_service.rs","byte_start":25033,"byte_end":25132,"line_start":652,"line_end":652,"column_start":13,"column_end":112,"is_primary":false,"text":[{"text":"            AuthUrl::new(\"https://login.microsoftonline.com/common/oauth2/v2.0/authorize\".to_string()).unwrap(),","highlight_start":13,"highlight_end":112}],"label":"unexpected argument #3 of type `AuthUrl`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/services/oauth_service.rs","byte_start":25146,"byte_end":25248,"line_start":653,"line_end":653,"column_start":13,"column_end":115,"is_primary":false,"text":[{"text":"            Some(TokenUrl::new(\"https://login.microsoftonline.com/common/oauth2/v2.0/token\".to_string()).unwrap()),","highlight_start":13,"highlight_end":115}],"label":"unexpected argument #4 of type `std::option::Option<TokenUrl>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/services/oauth_service.rs","byte_start":24853,"byte_end":24869,"line_start":649,"line_end":649,"column_start":22,"column_end":38,"is_primary":true,"text":[{"text":"        let client = BasicClient::new(","highlight_start":22,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"associated function defined here","code":null,"level":"note","spans":[{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/oauth2-5.0.0/src/client.rs","byte_start":7611,"byte_end":7614,"line_start":190,"line_end":190,"column_start":12,"column_end":15,"is_primary":true,"text":[{"text":"    pub fn new(client_id: ClientId) -> Self {","highlight_start":12,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"remove the extra arguments","code":null,"level":"help","spans":[{"file_name":"src/services/oauth_service.rs","byte_start":24937,"byte_end":25019,"line_start":650,"line_end":651,"column_start":67,"column_end":81,"is_primary":true,"text":[{"text":"            ClientId::new(self.config.microsoft.client_id.clone()),","highlight_start":67,"highlight_end":68},{"text":"            Some(ClientSecret::new(self.config.microsoft.client_secret.clone())),","highlight_start":1,"highlight_end":81}],"label":null,"suggested_replacement":"","suggestion_applicability":"HasPlaceholders","expansion":null},{"file_name":"src/services/oauth_service.rs","byte_start":25019,"byte_end":25132,"line_start":651,"line_end":652,"column_start":81,"column_end":112,"is_primary":true,"text":[{"text":"            Some(ClientSecret::new(self.config.microsoft.client_secret.clone())),","highlight_start":81,"highlight_end":82},{"text":"            AuthUrl::new(\"https://login.microsoftonline.com/common/oauth2/v2.0/authorize\".to_string()).unwrap(),","highlight_start":1,"highlight_end":112}],"label":null,"suggested_replacement":"","suggestion_applicability":"HasPlaceholders","expansion":null},{"file_name":"src/services/oauth_service.rs","byte_start":25132,"byte_end":25248,"line_start":652,"line_end":653,"column_start":112,"column_end":115,"is_primary":true,"text":[{"text":"            AuthUrl::new(\"https://login.microsoftonline.com/common/oauth2/v2.0/authorize\".to_string()).unwrap(),","highlight_start":112,"highlight_end":113},{"text":"            Some(TokenUrl::new(\"https://login.microsoftonline.com/common/oauth2/v2.0/token\".to_string()).unwrap()),","highlight_start":1,"highlight_end":115}],"label":null,"suggested_replacement":"","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0061]\u001b[0m\u001b[0m\u001b[1m: this function takes 1 argument but 4 arguments were supplied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/oauth_service.rs:649:22\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m649\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let client = BasicClient::new(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m650\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            ClientId::new(self.config.microsoft.client_id.clone()),\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m651\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            Some(ClientSecret::new(self.config.microsoft.client_secret.clone())),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------------------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12munexpected argument #2 of type `std::option::Option<ClientSecret>`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m652\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            AuthUrl::new(\"https://login.microsoftonline.com/common/oauth2/v2.0/authorize\".to_string()).unwrap(),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m---------------------------------------------------------------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12munexpected argument #3 of type `AuthUrl`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m653\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            Some(TokenUrl::new(\"https://login.microsoftonline.com/common/oauth2/v2.0/token\".to_string()).unwrap()),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m------------------------------------------------------------------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12munexpected argument #4 of type `std::option::Option<TokenUrl>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: associated function defined here\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/oauth2-5.0.0/src/client.rs:190:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m190\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new(client_id: ClientId) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: remove the extra arguments\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m650\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m            ClientId::new(self.config.microsoft.client_id.clone())\u001b[0m\u001b[0m\u001b[38;5;9m,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m651\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m-             Some(ClientSecret::new(self.config.microsoft.client_secret.clone())),\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m650\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m            ClientId::new(self.config.microsoft.client_id.clone()),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `authorize_url` found for struct `Client<StandardErrorResponse<BasicErrorResponseType>, StandardTokenResponse<EmptyExtraTokenFields, ...>, ..., ..., ...>` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src/services/oauth_service.rs","byte_start":25293,"byte_end":25313,"line_start":656,"line_end":657,"column_start":32,"column_end":14,"is_primary":false,"text":[{"text":"        let mut auth_request = client","highlight_start":32,"highlight_end":38},{"text":"            .authorize_url(|| CsrfToken::new(state.to_string()))","highlight_start":1,"highlight_end":14}],"label":"","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/services/oauth_service.rs","byte_start":25313,"byte_end":25326,"line_start":657,"line_end":657,"column_start":14,"column_end":27,"is_primary":true,"text":[{"text":"            .authorize_url(|| CsrfToken::new(state.to_string()))","highlight_start":14,"highlight_end":27}],"label":"method not found in `Client<StandardErrorResponse<BasicErrorResponseType>, StandardTokenResponse<EmptyExtraTokenFields, ...>, ..., ..., ...>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"the method was found for\n- `oauth2::Client<TE, TR, TIR, RT, TRE, EndpointMaybeSet, HasDeviceAuthUrl, HasIntrospectionUrl, HasRevocationUrl, HasTokenUrl>`\n- `oauth2::Client<TE, TR, TIR, RT, TRE, EndpointSet, HasDeviceAuthUrl, HasIntrospectionUrl, HasRevocationUrl, HasTokenUrl>`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m: no method named `authorize_url` found for struct `Client<StandardErrorResponse<BasicErrorResponseType>, StandardTokenResponse<EmptyExtraTokenFields, ...>, ..., ..., ...>` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/services/oauth_service.rs:657:14\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m656\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m        let mut auth_request = client\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m ________________________________-\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m657\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .authorize_url(|| CsrfToken::new(state.to_string()))\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmethod not found in `Client<StandardErrorResponse<BasicErrorResponseType>, StandardTokenResponse<EmptyExtraTokenFields, ...>, ..., ..., ...>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|_____________|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: the method was found for\u001b[0m\n\u001b[0m            - `oauth2::Client<TE, TR, TIR, RT, TRE, EndpointMaybeSet, HasDeviceAuthUrl, HasIntrospectionUrl, HasRevocationUrl, HasTokenUrl>`\u001b[0m\n\u001b[0m            - `oauth2::Client<TE, TR, TIR, RT, TRE, EndpointSet, HasDeviceAuthUrl, HasIntrospectionUrl, HasRevocationUrl, HasTokenUrl>`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"use of deprecated method `rand::Rng::gen_range`: Renamed to `random_range`","code":{"code":"deprecated","explanation":null},"level":"warning","spans":[{"file_name":"src/utils/crypto.rs","byte_start":541,"byte_end":550,"line_start":18,"line_end":18,"column_start":27,"column_end":36,"is_primary":true,"text":[{"text":"            let idx = rng.gen_range(0..charset.len());","highlight_start":27,"highlight_end":36}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: use of deprecated method `rand::Rng::gen_range`: Renamed to `random_range`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/utils/crypto.rs:18:27\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m18\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            let idx = rng.gen_range(0..charset.len());\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                           \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"failed to resolve: use of unresolved module or unlinked crate `config`","code":{"code":"E0433","explanation":"An undeclared crate, module, or type was used.\n\nErroneous code example:\n\n```compile_fail,E0433\nlet map = HashMap::new();\n// error: failed to resolve: use of undeclared type `HashMap`\n```\n\nPlease verify you didn't misspell the type/module's name or that you didn't\nforget to import it:\n\n```\nuse std::collections::HashMap; // HashMap has been imported.\nlet map: HashMap<u32, u32> = HashMap::new(); // So it can be used!\n```\n\nIf you've expected to use a crate name:\n\n```compile_fail\nuse ferris_wheel::BigO;\n// error: failed to resolve: use of undeclared module or unlinked crate\n```\n\nMake sure the crate has been added as a dependency in `Cargo.toml`.\n\nTo use a module from your current crate, add the `crate::` prefix to the path.\n"},"level":"error","spans":[{"file_name":"src/config/mod.rs","byte_start":1561,"byte_end":1567,"line_start":68,"line_end":68,"column_start":39,"column_end":45,"is_primary":true,"text":[{"text":"    pub fn from_env() -> Result<Self, config::ConfigError> {","highlight_start":39,"highlight_end":45}],"label":"use of unresolved module or unlinked crate `config`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if you wanted to use a crate named `config`, use `cargo add config` to add it to your `Cargo.toml`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"a struct with a similar name exists","code":null,"level":"help","spans":[{"file_name":"src/config/mod.rs","byte_start":1561,"byte_end":1567,"line_start":68,"line_end":68,"column_start":39,"column_end":45,"is_primary":true,"text":[{"text":"    pub fn from_env() -> Result<Self, config::ConfigError> {","highlight_start":39,"highlight_end":45}],"label":null,"suggested_replacement":"Config","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0433]\u001b[0m\u001b[0m\u001b[1m: failed to resolve: use of unresolved module or unlinked crate `config`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/config/mod.rs:68:39\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m68\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn from_env() -> Result<Self, config::ConfigError> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9muse of unresolved module or unlinked crate `config`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mhelp: a struct with a similar name exists (notice the capitalization): `Config`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: if you wanted to use a crate named `config`, use `cargo add config` to add it to your `Cargo.toml`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"failed to resolve: use of unresolved module or unlinked crate `config`","code":{"code":"E0433","explanation":"An undeclared crate, module, or type was used.\n\nErroneous code example:\n\n```compile_fail,E0433\nlet map = HashMap::new();\n// error: failed to resolve: use of undeclared type `HashMap`\n```\n\nPlease verify you didn't misspell the type/module's name or that you didn't\nforget to import it:\n\n```\nuse std::collections::HashMap; // HashMap has been imported.\nlet map: HashMap<u32, u32> = HashMap::new(); // So it can be used!\n```\n\nIf you've expected to use a crate name:\n\n```compile_fail\nuse ferris_wheel::BigO;\n// error: failed to resolve: use of undeclared module or unlinked crate\n```\n\nMake sure the crate has been added as a dependency in `Cargo.toml`.\n\nTo use a module from your current crate, add the `crate::` prefix to the path.\n"},"level":"error","spans":[{"file_name":"src/config/mod.rs","byte_start":3785,"byte_end":3791,"line_start":118,"line_end":118,"column_start":62,"column_end":68,"is_primary":true,"text":[{"text":"    pub fn load_multi_app_config() -> Result<MultiAppConfig, config::ConfigError> {","highlight_start":62,"highlight_end":68}],"label":"use of unresolved module or unlinked crate `config`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if you wanted to use a crate named `config`, use `cargo add config` to add it to your `Cargo.toml`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"a struct with a similar name exists","code":null,"level":"help","spans":[{"file_name":"src/config/mod.rs","byte_start":3785,"byte_end":3791,"line_start":118,"line_end":118,"column_start":62,"column_end":68,"is_primary":true,"text":[{"text":"    pub fn load_multi_app_config() -> Result<MultiAppConfig, config::ConfigError> {","highlight_start":62,"highlight_end":68}],"label":null,"suggested_replacement":"Config","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0433]\u001b[0m\u001b[0m\u001b[1m: failed to resolve: use of unresolved module or unlinked crate `config`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/config/mod.rs:118:62\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m118\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn load_multi_app_config() -> Result<MultiAppConfig, config::ConfigError> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9muse of unresolved module or unlinked crate `config`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mhelp: a struct with a similar name exists (notice the capitalization): `Config`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: if you wanted to use a crate named `config`, use `cargo add config` to add it to your `Cargo.toml`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 28 previous errors; 51 warnings emitted","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: aborting due to 28 previous errors; 51 warnings emitted\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"Some errors have detailed explanations: E0061, E0432, E0433, E0599.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1mSome errors have detailed explanations: E0061, E0432, E0433, E0599.\u001b[0m\n"}
{"$message_type":"diagnostic","message":"For more information about an error, try `rustc --explain E0061`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1mFor more information about an error, try `rustc --explain E0061`.\u001b[0m\n"}
