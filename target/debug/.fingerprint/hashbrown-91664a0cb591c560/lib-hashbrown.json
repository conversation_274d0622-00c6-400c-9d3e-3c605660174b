{"rustc": 15497389221046826682, "features": "[\"ahash\", \"allocator-api2\", \"default\", \"inline-more\", \"raw\"]", "declared_features": "[\"ahash\", \"alloc\", \"allocator-api2\", \"compiler_builtins\", \"core\", \"default\", \"equivalent\", \"inline-more\", \"nightly\", \"raw\", \"rayon\", \"rkyv\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 9101038166729729440, "profile": 8276155916380437441, "path": 4562546525590704000, "deps": [[966925859616469517, "ahash", false, 17763798415537176090], [9150530836556604396, "allocator_api2", false, 8281001615053381216]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/hashbrown-91664a0cb591c560/dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}